<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Ozon Sync Wizard Form View -->
    <record id="view_ozon_sync_wizard_form" model="ir.ui.view">
        <field name="name">ozon.sync.wizard.form</field>
        <field name="model">ozon.sync.wizard</field>
        <field name="arch" type="xml">
            <form string="Ozon Synchronization">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="sync_type" widget="radio" options="{'horizontal': true}"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Sync Options">
                            <field name="force_sync" attrs="{'invisible': [('sync_type', 'in', ['orders', 'full'])]}"/>
                            <field name="batch_size"/>
                        </group>
                        <group string="Order Import Options" attrs="{'invisible': [('sync_type', 'not in', ['orders', 'full'])]}">
                            <field name="days_back"/>
                        </group>
                    </group>
                    
                    <group string="Product Selection" attrs="{'invisible': [('sync_type', 'in', ['orders', 'full'])]}">
                        <field name="product_ids" nolabel="1" 
                               placeholder="Leave empty to sync all enabled products"/>
                    </group>
                    
                    <div class="alert alert-info" role="alert" attrs="{'invisible': [('sync_type', '!=', 'products')]}">
                        <strong>Product Sync:</strong> This will sync product information (name, price, description) to Ozon marketplace.
                    </div>
                    
                    <div class="alert alert-info" role="alert" attrs="{'invisible': [('sync_type', '!=', 'stock')]}">
                        <strong>Stock Sync:</strong> This will update stock quantities on Ozon based on current Odoo inventory.
                    </div>
                    
                    <div class="alert alert-info" role="alert" attrs="{'invisible': [('sync_type', '!=', 'orders')]}">
                        <strong>Order Import:</strong> This will import recent orders from Ozon and create sales orders in Odoo.
                    </div>
                    
                    <div class="alert alert-warning" role="alert" attrs="{'invisible': [('sync_type', '!=', 'full')]}">
                        <strong>Full Sync:</strong> This will perform all synchronization operations: products, stock, and orders. This may take some time.
                    </div>
                </sheet>
                
                <footer>
                    <button name="action_start_sync" string="Start Synchronization" 
                            type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Ozon Sync Wizard Action -->
    <record id="action_ozon_sync_wizard" model="ir.actions.act_window">
        <field name="name">Ozon Synchronization</field>
        <field name="res_model">ozon.sync.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="product.model_product_template"/>
        <field name="binding_view_types">list</field>
    </record>

    <!-- Add Sync Wizard to Main Menu -->
    <menuitem id="menu_ozon_sync_wizard" 
              name="Manual Sync" 
              parent="menu_ozon_main" 
              action="action_ozon_sync_wizard" 
              sequence="50"/>
</odoo>
