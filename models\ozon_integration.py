# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import requests
import json
import logging
import time
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class OzonIntegrationLog(models.Model):
    _name = 'ozon.integration.log'
    _description = 'Ozon Integration Log'
    _order = 'create_date desc'
    _rec_name = 'operation_type'

    operation_type = fields.Selection([
        ('product_sync', 'Product Sync'),
        ('stock_sync', 'Stock Sync'),
        ('order_import', 'Order Import'),
        ('full_sync', 'Full Sync')
    ], string='Operation Type', required=True)
    
    status = fields.Selection([
        ('running', 'Running'),
        ('success', 'Success'),
        ('error', 'Error'),
        ('warning', 'Warning')
    ], string='Status', default='running')
    
    start_time = fields.Datetime(string='Start Time', default=fields.Datetime.now)
    end_time = fields.Datetime(string='End Time')
    duration = fields.Float(string='Duration (seconds)', compute='_compute_duration', store=True)
    
    success_count = fields.Integer(string='Success Count', default=0)
    error_count = fields.Integer(string='Error Count', default=0)
    skipped_count = fields.Integer(string='Skipped Count', default=0)
    
    message = fields.Text(string='Message')
    error_details = fields.Text(string='Error Details')
    
    config_id = fields.Many2one('ozon.config', string='Configuration', required=True)
    
    @api.depends('start_time', 'end_time')
    def _compute_duration(self):
        for record in self:
            if record.start_time and record.end_time:
                delta = record.end_time - record.start_time
                record.duration = delta.total_seconds()
            else:
                record.duration = 0.0


class OzonProductSync(models.Model):
    _name = 'ozon.product.sync'
    _description = 'Ozon Product Synchronization'
    _rec_name = 'product_id'

    product_id = fields.Many2one('product.template', string='Product', required=True)
    ozon_product_id = fields.Char(string='Ozon Product ID')
    ozon_offer_id = fields.Char(string='Ozon Offer ID')
    
    sync_status = fields.Selection([
        ('pending', 'Pending'),
        ('synced', 'Synced'),
        ('error', 'Error'),
        ('skipped', 'Skipped')
    ], string='Sync Status', default='pending')
    
    last_sync_date = fields.Datetime(string='Last Sync Date')
    last_error = fields.Text(string='Last Error')
    
    # Ozon specific fields
    ozon_category_id = fields.Integer(string='Ozon Category ID')
    ozon_price = fields.Float(string='Ozon Price')
    ozon_stock = fields.Integer(string='Ozon Stock')
    
    active = fields.Boolean(string='Active', default=True)
    
    def sync_to_ozon(self):
        """Sync this product to Ozon"""
        config = self.env['ozon.config'].get_active_config()
        
        try:
            # Prepare product data for Ozon
            product_data = self._prepare_ozon_product_data()
            
            # Check if product exists in Ozon
            existing_product = self._find_ozon_product(config)
            
            if existing_product:
                result = self._update_ozon_product(config, product_data)
            else:
                result = self._create_ozon_product(config, product_data)
            
            if result:
                self.sync_status = 'synced'
                self.last_sync_date = fields.Datetime.now()
                self.last_error = False
            else:
                self.sync_status = 'error'
                self.last_error = 'Failed to sync product to Ozon'
                
        except Exception as e:
            self.sync_status = 'error'
            self.last_error = str(e)
            _logger.error(f"Error syncing product {self.product_id.name}: {str(e)}")
    
    def _prepare_ozon_product_data(self):
        """Prepare product data for Ozon API"""
        product = self.product_id
        
        return {
            'attributes': [
                {
                    'complex_id': 0,
                    'id': 9048,  # Name attribute
                    'values': [{'value': product.name}]
                }
            ],
            'barcode': product.barcode or '',
            'description_category_id': self.ozon_category_id or 17028922,
            'currency_code': 'RUB',
            'depth': int(product.volume or 10),
            'dimension_unit': 'mm',
            'height': 100,
            'width': 100,
            'images': [],
            'name': product.name,
            'offer_id': product.default_code or str(product.id),
            'price': str(product.list_price),
            'old_price': str(product.list_price * 1.1),
            'vat': '0.1',
            'weight': int(product.weight * 1000) if product.weight else 100,
            'weight_unit': 'g'
        }
    
    def _find_ozon_product(self, config):
        """Find product in Ozon by SKU"""
        if not self.product_id.default_code:
            return None
            
        headers = config.get_headers()
        url = f"{config.base_url}/v2/product/list"
        
        payload = {
            'filter': {
                'offer_id': [self.product_id.default_code]
            },
            'limit': 1,
            'last_id': ''
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                items = result.get('result', {}).get('items', [])
                return items[0] if items else None
        except Exception as e:
            _logger.error(f"Error finding product in Ozon: {str(e)}")
            
        return None
    
    def _create_ozon_product(self, config, product_data):
        """Create product in Ozon"""
        headers = config.get_headers()
        url = f"{config.base_url}/v3/product/import"
        
        payload = {'items': [product_data]}
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                task_id = result.get('result', {}).get('task_id')
                if task_id:
                    self.ozon_offer_id = product_data['offer_id']
                    return True
        except Exception as e:
            _logger.error(f"Error creating product in Ozon: {str(e)}")
            
        return False
    
    def _update_ozon_product(self, config, product_data):
        """Update existing product in Ozon"""
        headers = config.get_headers()
        url = f"{config.base_url}/v1/product/import/prices"
        
        update_data = {
            'prices': [{
                'offer_id': product_data['offer_id'],
                'price': product_data['price'],
                'old_price': product_data['old_price'],
                'currency_code': product_data['currency_code']
            }]
        }
        
        try:
            response = requests.post(url, headers=headers, json=update_data, timeout=30)
            return response.status_code == 200
        except Exception as e:
            _logger.error(f"Error updating product in Ozon: {str(e)}")
            
        return False


class OzonStockSync(models.Model):
    _name = 'ozon.stock.sync'
    _description = 'Ozon Stock Synchronization'
    
    product_id = fields.Many2one('product.template', string='Product', required=True)
    odoo_stock = fields.Float(string='Odoo Stock')
    ozon_stock = fields.Integer(string='Ozon Stock')
    
    sync_status = fields.Selection([
        ('pending', 'Pending'),
        ('synced', 'Synced'),
        ('error', 'Error')
    ], string='Sync Status', default='pending')
    
    last_sync_date = fields.Datetime(string='Last Sync Date')
    last_error = fields.Text(string='Last Error')
    
    def sync_stock_to_ozon(self):
        """Sync stock to Ozon"""
        config = self.env['ozon.config'].get_active_config()
        
        if not self.product_id.default_code:
            self.sync_status = 'error'
            self.last_error = 'Product has no SKU (default_code)'
            return
        
        try:
            # Get current stock
            stock_qty = self._get_product_stock()
            self.odoo_stock = stock_qty
            
            # Update stock in Ozon
            if self._update_ozon_stock(config, stock_qty):
                self.sync_status = 'synced'
                self.ozon_stock = int(stock_qty)
                self.last_sync_date = fields.Datetime.now()
                self.last_error = False
            else:
                self.sync_status = 'error'
                self.last_error = 'Failed to update stock in Ozon'
                
        except Exception as e:
            self.sync_status = 'error'
            self.last_error = str(e)
            _logger.error(f"Error syncing stock for {self.product_id.name}: {str(e)}")
    
    def _get_product_stock(self):
        """Get current stock quantity for product"""
        config = self.env['ozon.config'].get_active_config()
        
        domain = [('product_id', '=', self.product_id.id)]
        
        if config.stock_location_ids:
            domain.append(('location_id', 'in', config.stock_location_ids.ids))
        else:
            domain.append(('location_id.usage', '=', 'internal'))
        
        quants = self.env['stock.quant'].search(domain)
        return sum(quants.mapped('quantity'))
    
    def _update_ozon_stock(self, config, quantity):
        """Update stock in Ozon"""
        headers = config.get_headers()
        url = f"{config.base_url}/v1/product/import/stocks"
        
        payload = {
            'stocks': [{
                'offer_id': self.product_id.default_code,
                'stock': int(max(0, quantity))
            }]
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            return response.status_code == 200
        except Exception as e:
            _logger.error(f"Error updating stock in Ozon: {str(e)}")
            
        return False
