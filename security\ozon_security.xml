<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Ozon Integration Security Groups -->
    <record id="group_ozon_user" model="res.groups">
        <field name="name">Ozon User</field>
        <field name="category_id" ref="base.module_category_sales"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        <field name="comment">Basic Ozon integration access - can view products and orders</field>
    </record>

    <record id="group_ozon_manager" model="res.groups">
        <field name="name">Ozon Manager</field>
        <field name="category_id" ref="base.module_category_sales"/>
        <field name="implied_ids" eval="[(4, ref('group_ozon_user'))]"/>
        <field name="comment">Ozon integration management - can sync products, manage stock, and configure settings</field>
    </record>

    <record id="group_ozon_admin" model="res.groups">
        <field name="name">Ozon Administrator</field>
        <field name="category_id" ref="base.module_category_sales"/>
        <field name="implied_ids" eval="[(4, ref('group_ozon_manager'))]"/>
        <field name="comment">Full Ozon integration access - can configure API settings and manage all aspects</field>
    </record>

    <!-- Record Rules -->
    
    <!-- Ozon Config Rules -->
    <record id="rule_ozon_config_user" model="ir.rule">
        <field name="name">Ozon Config: User Access</field>
        <field name="model_id" ref="model_ozon_config"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="rule_ozon_config_admin" model="ir.rule">
        <field name="name">Ozon Config: Admin Access</field>
        <field name="model_id" ref="model_ozon_config"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Integration Log Rules -->
    <record id="rule_ozon_log_user" model="ir.rule">
        <field name="name">Ozon Logs: User Access</field>
        <field name="model_id" ref="model_ozon_integration_log"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Product Sync Rules -->
    <record id="rule_ozon_product_sync_user" model="ir.rule">
        <field name="name">Ozon Product Sync: User Access</field>
        <field name="model_id" ref="model_ozon_product_sync"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="rule_ozon_product_sync_manager" model="ir.rule">
        <field name="name">Ozon Product Sync: Manager Access</field>
        <field name="model_id" ref="model_ozon_product_sync"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Stock Sync Rules -->
    <record id="rule_ozon_stock_sync_user" model="ir.rule">
        <field name="name">Ozon Stock Sync: User Access</field>
        <field name="model_id" ref="model_ozon_stock_sync"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="rule_ozon_stock_sync_manager" model="ir.rule">
        <field name="name">Ozon Stock Sync: Manager Access</field>
        <field name="model_id" ref="model_ozon_stock_sync"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Sync Wizard Rules -->
    <record id="rule_ozon_sync_wizard_manager" model="ir.rule">
        <field name="name">Ozon Sync Wizard: Manager Access</field>
        <field name="model_id" ref="model_ozon_sync_wizard"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_ozon_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
