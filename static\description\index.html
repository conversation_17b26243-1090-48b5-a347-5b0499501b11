<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ozon Marketplace Integration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 40px; }
        .feature { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .feature h3 { margin-top: 0; color: #007bff; }
        .screenshot { text-align: center; margin: 20px 0; }
        .screenshot img { max-width: 100%; border: 1px solid #ddd; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛒 Ozon Marketplace Integration</h1>
        <p>Complete integration between Odoo ERP and Ozon marketplace</p>
    </div>

    <div class="feature">
        <h3>📦 Product Synchronization</h3>
        <p>Automatically sync your products from Odoo to Ozon marketplace with intelligent mapping and batch processing.</p>
        <ul>
            <li>Smart product mapping with SKU-based matching</li>
            <li>Batch processing for large catalogs</li>
            <li>Automatic price and description updates</li>
            <li>Category management and mapping</li>
        </ul>
    </div>

    <div class="feature">
        <h3>📊 Stock Management</h3>
        <p>Keep your Ozon inventory synchronized with your Odoo stock levels in real-time.</p>
        <ul>
            <li>Real-time stock synchronization</li>
            <li>Multi-location inventory support</li>
            <li>Automatic stock updates on changes</li>
            <li>Stock level monitoring and alerts</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🛍️ Order Management</h3>
        <p>Automatically import orders from Ozon and create corresponding sales orders in Odoo.</p>
        <ul>
            <li>Automatic order import from Ozon</li>
            <li>Customer creation and matching</li>
            <li>Order status synchronization</li>
            <li>Delivery and payment method tracking</li>
        </ul>
    </div>

    <div class="feature">
        <h3>⚙️ Configuration & Monitoring</h3>
        <p>Easy setup and comprehensive monitoring of your integration.</p>
        <ul>
            <li>Simple API configuration</li>
            <li>Comprehensive logging and error tracking</li>
            <li>Scheduled automatic synchronization</li>
            <li>Manual sync options and wizards</li>
            <li>Integration status dashboard</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🔧 Technical Features</h3>
        <ul>
            <li>Robust error handling with retry mechanisms</li>
            <li>Webhook support for real-time updates</li>
            <li>RESTful API endpoints for external integration</li>
            <li>Comprehensive security and access control</li>
            <li>Multi-user support with role-based permissions</li>
        </ul>
    </div>

    <div class="feature">
        <h3>📋 Requirements</h3>
        <ul>
            <li>Valid Ozon Seller account with API access</li>
            <li>Ozon API credentials (Client ID and API Key)</li>
            <li>Odoo 17.0 or later</li>
            <li>Internet connection for API communication</li>
        </ul>
    </div>

    <div class="feature">
        <h3>🚀 Getting Started</h3>
        <ol>
            <li>Install the module from Apps menu</li>
            <li>Go to Ozon Integration → Configuration → Ozon Settings</li>
            <li>Enter your Ozon API credentials</li>
            <li>Test the connection</li>
            <li>Enable Ozon sync on your products</li>
            <li>Start synchronizing!</li>
        </ol>
    </div>

    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 8px;">
        <h3>Need Help?</h3>
        <p>Check the module documentation or contact support for assistance with setup and configuration.</p>
    </div>
</body>
</html>
