<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Cron Job: Sync Products to Ozon -->
    <record id="cron_sync_products_to_ozon" model="ir.cron">
        <field name="name">Ozon: Sync Products</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="code">model.cron_sync_products_to_ozon()</field>
        <field name="interval_number">6</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="doall">False</field>
    </record>

    <!-- Cron Job: Sync Stock to Ozon -->
    <record id="cron_sync_stock_to_ozon" model="ir.cron">
        <field name="name">Ozon: Sync Stock</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="code">model.cron_sync_stock_to_ozon()</field>
        <field name="interval_number">2</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="doall">False</field>
    </record>

    <!-- Cron Job: Import Orders from Ozon -->
    <record id="cron_import_ozon_orders" model="ir.cron">
        <field name="name">Ozon: Import Orders</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="state">code</field>
        <field name="code">model.cron_import_ozon_orders()</field>
        <field name="interval_number">30</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="doall">False</field>
    </record>

    <!-- Cron Job: Cleanup Old Logs -->
    <record id="cron_cleanup_ozon_logs" model="ir.cron">
        <field name="name">Ozon: Cleanup Old Logs</field>
        <field name="model_id" ref="model_ozon_integration_log"/>
        <field name="state">code</field>
        <field name="code">
# Cleanup logs older than 30 days
from datetime import datetime, timedelta
cutoff_date = datetime.now() - timedelta(days=30)
old_logs = model.search([('create_date', '&lt;', cutoff_date)])
old_logs.unlink()
        </field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="doall">False</field>
    </record>
</odoo>
