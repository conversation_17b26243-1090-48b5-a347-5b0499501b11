# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.exceptions import AccessError, UserError
import json
import logging

_logger = logging.getLogger(__name__)


class OzonController(http.Controller):

    @http.route('/ozon/webhook', type='json', auth='none', methods=['POST'], csrf=False)
    def ozon_webhook(self, **kwargs):
        """Handle Ozon webhooks"""
        try:
            data = request.jsonrequest
            _logger.info(f"Received Ozon webhook: {data}")
            
            # Process webhook based on type
            webhook_type = data.get('type')
            
            if webhook_type == 'order_status_changed':
                return self._handle_order_status_webhook(data)
            elif webhook_type == 'product_updated':
                return self._handle_product_webhook(data)
            else:
                _logger.warning(f"Unknown webhook type: {webhook_type}")
                return {'status': 'ignored', 'message': 'Unknown webhook type'}
                
        except Exception as e:
            _logger.error(f"Error processing Ozon webhook: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_order_status_webhook(self, data):
        """Handle order status change webhook"""
        try:
            posting_number = data.get('posting_number')
            new_status = data.get('status')
            
            if not posting_number:
                return {'status': 'error', 'message': 'Missing posting_number'}
            
            # Find the order in Odoo
            order = request.env['sale.order'].sudo().search([
                ('ozon_posting_number', '=', posting_number)
            ], limit=1)
            
            if order:
                order.ozon_order_status = new_status
                _logger.info(f"Updated order {posting_number} status to {new_status}")
                return {'status': 'success', 'message': 'Order status updated'}
            else:
                _logger.warning(f"Order {posting_number} not found in Odoo")
                return {'status': 'not_found', 'message': 'Order not found'}
                
        except Exception as e:
            _logger.error(f"Error handling order webhook: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_product_webhook(self, data):
        """Handle product update webhook"""
        try:
            offer_id = data.get('offer_id')
            
            if not offer_id:
                return {'status': 'error', 'message': 'Missing offer_id'}
            
            # Find the product in Odoo
            product = request.env['product.template'].sudo().search([
                ('default_code', '=', offer_id)
            ], limit=1)
            
            if product:
                # Update product sync status to trigger re-sync if needed
                product.ozon_sync_status = 'pending'
                _logger.info(f"Marked product {offer_id} for re-sync")
                return {'status': 'success', 'message': 'Product marked for sync'}
            else:
                _logger.warning(f"Product {offer_id} not found in Odoo")
                return {'status': 'not_found', 'message': 'Product not found'}
                
        except Exception as e:
            _logger.error(f"Error handling product webhook: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    @http.route('/ozon/sync/products', type='json', auth='user', methods=['POST'])
    def sync_products_api(self, product_ids=None, force_sync=False):
        """API endpoint to sync products"""
        try:
            # Check user permissions
            if not request.env.user.has_group('base.group_user'):
                raise AccessError(_('Access denied'))
            
            # Get products to sync
            if product_ids:
                products = request.env['product.template'].browse(product_ids)
            else:
                domain = [('ozon_sync_enabled', '=', True), ('default_code', '!=', False)]
                if not force_sync:
                    domain.append(('ozon_sync_status', 'in', ['not_synced', 'pending', 'error']))
                products = request.env['product.template'].search(domain, limit=50)
            
            success_count = 0
            error_count = 0
            errors = []
            
            for product in products:
                try:
                    product.action_sync_to_ozon()
                    if product.ozon_sync_status == 'synced':
                        success_count += 1
                    else:
                        error_count += 1
                        errors.append(f"{product.name}: {product.ozon_last_error}")
                except Exception as e:
                    error_count += 1
                    errors.append(f"{product.name}: {str(e)}")
            
            return {
                'status': 'success',
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors[:10]  # Limit error list
            }
            
        except Exception as e:
            _logger.error(f"Error in sync products API: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    @http.route('/ozon/sync/stock', type='json', auth='user', methods=['POST'])
    def sync_stock_api(self, product_ids=None):
        """API endpoint to sync stock"""
        try:
            # Check user permissions
            if not request.env.user.has_group('stock.group_stock_user'):
                raise AccessError(_('Access denied'))
            
            # Get products to sync
            if product_ids:
                products = request.env['product.template'].browse(product_ids)
            else:
                products = request.env['product.template'].search([
                    ('ozon_sync_enabled', '=', True),
                    ('ozon_sync_status', '=', 'synced'),
                    ('default_code', '!=', False)
                ], limit=50)
            
            success_count = 0
            error_count = 0
            errors = []
            
            for product in products:
                try:
                    product.action_sync_stock_to_ozon()
                    
                    # Check sync result
                    stock_sync = request.env['ozon.stock.sync'].search([
                        ('product_id', '=', product.id)
                    ], limit=1)
                    
                    if stock_sync and stock_sync.sync_status == 'synced':
                        success_count += 1
                    else:
                        error_count += 1
                        errors.append(f"{product.name}: {stock_sync.last_error if stock_sync else 'Unknown error'}")
                        
                except Exception as e:
                    error_count += 1
                    errors.append(f"{product.name}: {str(e)}")
            
            return {
                'status': 'success',
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors[:10]
            }
            
        except Exception as e:
            _logger.error(f"Error in sync stock API: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    @http.route('/ozon/import/orders', type='json', auth='user', methods=['POST'])
    def import_orders_api(self, days_back=7):
        """API endpoint to import orders"""
        try:
            # Check user permissions
            if not request.env.user.has_group('sales_team.group_sale_salesman'):
                raise AccessError(_('Access denied'))
            
            # Import orders
            request.env['sale.order'].import_ozon_orders(days_back)
            
            # Get latest import statistics
            log = request.env['ozon.integration.log'].search([
                ('operation_type', '=', 'order_import')
            ], limit=1, order='create_date desc')
            
            if log:
                return {
                    'status': 'success',
                    'success_count': log.success_count,
                    'error_count': log.error_count,
                    'skipped_count': log.skipped_count,
                    'message': log.message
                }
            else:
                return {
                    'status': 'success',
                    'message': 'Order import completed'
                }
                
        except Exception as e:
            _logger.error(f"Error in import orders API: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    @http.route('/ozon/status', type='json', auth='user', methods=['GET'])
    def get_integration_status(self):
        """Get integration status and statistics"""
        try:
            config = request.env['ozon.config'].search([('active', '=', True)], limit=1)
            
            if not config:
                return {'status': 'not_configured', 'message': 'No active configuration found'}
            
            # Get statistics
            total_products = request.env['product.template'].search_count([
                ('ozon_sync_enabled', '=', True)
            ])
            
            synced_products = request.env['product.template'].search_count([
                ('ozon_sync_enabled', '=', True),
                ('ozon_sync_status', '=', 'synced')
            ])
            
            pending_products = request.env['product.template'].search_count([
                ('ozon_sync_enabled', '=', True),
                ('ozon_sync_status', '=', 'pending')
            ])
            
            error_products = request.env['product.template'].search_count([
                ('ozon_sync_enabled', '=', True),
                ('ozon_sync_status', '=', 'error')
            ])
            
            ozon_orders = request.env['sale.order'].search_count([
                ('is_ozon_order', '=', True)
            ])
            
            # Get recent logs
            recent_logs = request.env['ozon.integration.log'].search([
                ('create_date', '>=', fields.Datetime.now() - timedelta(days=7))
            ], limit=10, order='create_date desc')
            
            return {
                'status': 'configured',
                'config': {
                    'name': config.name,
                    'last_sync_date': config.last_sync_date,
                    'last_sync_status': config.last_sync_status
                },
                'statistics': {
                    'total_products': total_products,
                    'synced_products': synced_products,
                    'pending_products': pending_products,
                    'error_products': error_products,
                    'ozon_orders': ozon_orders
                },
                'recent_logs': [{
                    'operation_type': log.operation_type,
                    'status': log.status,
                    'create_date': log.create_date,
                    'success_count': log.success_count,
                    'error_count': log.error_count
                } for log in recent_logs]
            }
            
        except Exception as e:
            _logger.error(f"Error getting integration status: {str(e)}")
            return {'status': 'error', 'message': str(e)}
