#!/usr/bin/env python3
"""
Simple Ozon API Test Script
===========================

This script tests basic Ozon API connectivity with different endpoints
to help diagnose API access issues.
"""

import requests
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_ozon_endpoints():
    """Test different Ozon API endpoints to find working ones"""
    
    client_id = os.getenv("OZON_CLIENT_ID")
    api_key = os.getenv("OZON_API_KEY")
    
    if not client_id or not api_key:
        print("❌ Missing Ozon API credentials")
        return False
    
    headers = {
        "Client-Id": client_id,
        "Api-Key": api_key,
        "Content-Type": "application/json"
    }
    
    # List of endpoints to test
    endpoints = [
        {
            "name": "Product List v2",
            "url": "https://api-seller.ozon.ru/v2/product/list",
            "payload": {"filter": {}, "limit": 1, "last_id": ""}
        },
        {
            "name": "Product List v1", 
            "url": "https://api-seller.ozon.ru/v1/product/list",
            "payload": {"filter": {}, "limit": 1, "last_id": ""}
        },
        {
            "name": "Product Info Stocks",
            "url": "https://api-seller.ozon.ru/v3/product/info/stocks",
            "payload": {"filter": {"visibility": "ALL"}, "limit": 1}
        },
        {
            "name": "Category Tree",
            "url": "https://api-seller.ozon.ru/v1/category/tree",
            "payload": {"language": "DEFAULT"}
        }
    ]
    
    print(f"Testing Ozon API with Client ID: {client_id[:8]}...")
    print("=" * 60)
    
    working_endpoints = []
    
    for endpoint in endpoints:
        print(f"\n🔍 Testing {endpoint['name']}...")
        print(f"   URL: {endpoint['url']}")
        
        try:
            response = requests.post(
                endpoint['url'], 
                headers=headers, 
                json=endpoint['payload'], 
                timeout=30
            )
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ SUCCESS - Response received")
                print(f"   Response keys: {list(result.keys())}")
                working_endpoints.append(endpoint['name'])
                
                # Show sample data if available
                if 'result' in result:
                    if isinstance(result['result'], dict):
                        print(f"   Result keys: {list(result['result'].keys())}")
                    elif isinstance(result['result'], list):
                        print(f"   Result items: {len(result['result'])}")
                        
            elif response.status_code == 401:
                print(f"   ❌ UNAUTHORIZED - Check API credentials")
            elif response.status_code == 403:
                print(f"   ❌ FORBIDDEN - API access denied")
            elif response.status_code == 404:
                print(f"   ❌ NOT FOUND - Endpoint may not exist")
            else:
                print(f"   ❌ ERROR - HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"   ❌ TIMEOUT - Request timed out")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ CONNECTION ERROR - Cannot reach server")
        except Exception as e:
            print(f"   ❌ ERROR - {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    
    if working_endpoints:
        print(f"✅ Working endpoints: {', '.join(working_endpoints)}")
        print("🎉 Ozon API access is working!")
        return True
    else:
        print("❌ No working endpoints found")
        print("💡 Suggestions:")
        print("   1. Verify your Ozon API credentials")
        print("   2. Check if your Ozon seller account has API access enabled")
        print("   3. Ensure you're using the correct API keys from Ozon Seller Portal")
        print("   4. Check if there are any IP restrictions on your API access")
        return False

if __name__ == "__main__":
    test_ozon_endpoints()
