# Odoo-Ozon Integration System

A comprehensive integration system that synchronizes products, stock, and sales orders between Odoo ERP and Ozon marketplace.

## Features

### 🔄 Product Synchronization
- **Odoo → Ozon**: Automatically sync all products from Odoo to Ozon
- **Smart Mapping**: Intelligent mapping of Odoo product fields to Ozon format
- **Create/Update Logic**: Creates new products or updates existing ones based on SKU
- **Batch Processing**: Processes products in configurable batches for optimal performance

### 📦 Stock Synchronization
- **Real-time Stock Sync**: Sync current stock quantities from Odoo to Ozon
- **Multi-location Support**: Aggregates stock from all internal locations in Odoo
- **Automatic Updates**: Updates stock levels for all integrated products

### 🛒 Sales Order Synchronization
- **Ozon → Odoo**: Import delivered orders from Ozon to Odoo
- **Customer Management**: Automatically creates or matches customers
- **Order Line Mapping**: Maps Ozon order items to Odoo products
- **Duplicate Prevention**: Prevents duplicate order creation

### 📊 Comprehensive Logging
- **Detailed Logs**: Logs every stage of the integration process
- **File-based Logging**: Saves logs to files with timestamps
- **Error Tracking**: Comprehensive error logging with stack traces
- **Progress Monitoring**: Track success/failure rates for each operation

## Installation

1. **Clone or download the integration files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual credentials
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required: Ozon API Credentials
OZON_CLIENT_ID=your_client_id
OZON_API_KEY=your_api_key

# Optional: Odoo Configuration (can be set in code)
ODOO_HOSTNAME=your_odoo_server
ODOO_DATABASE=your_database
ODOO_LOGIN=your_username
ODOO_PASSWORD=your_password
```

### Odoo Configuration

The system uses the existing Odoo connection from your `main.py`. You can either:
- Use environment variables (recommended for production)
- Modify the hardcoded values in the `IntegrationConfig` class

## Usage

### Basic Usage

Run the complete integration:
```bash
python integrate-odoo-ozon.py
```

### Advanced Usage

You can also use individual components:

```python
from integrate_odoo_ozon import OdooOzonIntegrator

# Initialize integrator
integrator = OdooOzonIntegrator()

# Run individual sync operations
product_results = integrator.sync_products_to_ozon()
stock_results = integrator.sync_stock_to_ozon()
order_results = integrator.sync_orders_from_ozon(days_back=30)
```

## Integration Process

The system follows this process:

1. **Configuration Validation**: Validates all required API credentials
2. **Product Sync**: 
   - Fetches all active, saleable products from Odoo
   - Maps product data to Ozon format
   - Creates new products or updates existing ones in Ozon
3. **Stock Sync**:
   - Retrieves current stock quantities from Odoo
   - Updates stock levels in Ozon for all synced products
4. **Order Sync**:
   - Fetches recent orders from Ozon
   - Creates corresponding sales orders in Odoo
   - Handles customer creation and product mapping

## Logging

### Log Files
- Location: `logs/odoo_ozon_integration.log`
- Format: Timestamped entries with operation details
- Rotation: Manual (consider implementing log rotation for production)

### Log Levels
- **INFO**: General operation progress
- **WARNING**: Non-critical issues (e.g., products without SKU)
- **ERROR**: Critical errors with full stack traces

### Sample Log Output
```
2025-09-03 10:30:15 - OdooOzonIntegration - INFO - [INTEGRATION_START] Starting full Odoo-Ozon integration
2025-09-03 10:30:16 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Successfully connected to Odoo
2025-09-03 10:30:17 - OdooOzonIntegration - INFO - [PRODUCT_SYNC] Product CREATE: ID=PROD001, Name='Sample Product', Status=SUCCESS
```

## Error Handling

### Retry Mechanism
- Automatic retry for API failures (configurable attempts)
- Exponential backoff for rate limiting
- Graceful handling of network issues

### Error Recovery
- Continues processing even if individual items fail
- Detailed error logging for troubleshooting
- Summary reports showing success/failure counts

## Customization

### Product Mapping
Modify the `_map_odoo_to_ozon_product` method to customize how Odoo products are mapped to Ozon format.

### Configuration
Adjust the `IntegrationConfig` class to modify:
- Batch sizes
- Retry attempts
- API endpoints
- Default values

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify Ozon API credentials in `.env` file
   - Check Odoo connection parameters

2. **Product Sync Failures**
   - Ensure products have SKU (default_code) in Odoo
   - Verify Ozon category mappings

3. **Stock Sync Issues**
   - Check Odoo stock location configuration
   - Verify product exists in both systems

4. **Order Sync Problems**
   - Ensure product SKUs match between systems
   - Check customer data requirements

### Debug Mode
Enable detailed logging by setting `INTEGRATION_LOG_LEVEL=DEBUG` in your `.env` file.

## Production Deployment

### Scheduling
Consider running the integration on a schedule:
```bash
# Add to crontab for hourly sync
0 * * * * /path/to/python /path/to/integrate-odoo-ozon.py
```

### Monitoring
- Monitor log files for errors
- Set up alerts for integration failures
- Track sync success rates

## Support

For issues or questions:
1. Check the log files for detailed error information
2. Verify configuration settings
3. Test individual components separately

## License

This integration system is provided as-is for educational and commercial use.
