# Ozon Integration Addon Deployment Guide

## Prerequisites

- Access to your Odoo server (SSH or file manager)
- Odoo administrator privileges
- Python packages: `requests`, `python-dotenv` (install if not present)

## Deployment Steps

### Step 1: Upload Addon to Server

1. **Locate your Odoo addons directory**:
   ```bash
   # Common locations:
   /opt/odoo/addons/
   /usr/lib/python3/dist-packages/odoo/addons/
   /var/lib/odoo/addons/
   # Or custom path specified in odoo.conf
   ```

2. **Create the addon directory**:
   ```bash
   sudo mkdir -p /path/to/odoo/addons/ozon_integration
   ```

3. **Upload all addon files** to `/path/to/odoo/addons/ozon_integration/`

### Step 2: Install Python Dependencies

```bash
# Install required Python packages
sudo pip3 install requests python-dotenv

# Or if using virtual environment:
source /path/to/odoo/venv/bin/activate
pip install requests python-dotenv
```

### Step 3: Set Permissions

```bash
# Set correct ownership and permissions
sudo chown -R odoo:odoo /path/to/odoo/addons/ozon_integration
sudo chmod -R 755 /path/to/odoo/addons/ozon_integration
```

### Step 4: Update Odoo Configuration

Edit your `odoo.conf` file:

```ini
[options]
addons_path = /path/to/odoo/addons,/path/to/other/addons,/path/to/odoo/addons/ozon_integration
```

### Step 5: Restart Odoo Service

```bash
# Restart Odoo service
sudo systemctl restart odoo
# or
sudo service odoo restart
```

### Step 6: Install the Addon

1. **Login to Odoo** as administrator
2. **Go to Apps menu**
3. **Click "Update Apps List"**
4. **Search for "Ozon"**
5. **Click "Install"**

## Configuration After Installation

### 1. Configure API Credentials

1. Go to **Ozon Integration → Configuration → Ozon Settings**
2. Create new configuration:
   - **Name**: Your configuration name
   - **Client ID**: Your Ozon API Client ID
   - **API Key**: Your Ozon API Key
   - **Base URL**: `https://api-seller.ozon.ru`
3. **Test Connection** to verify credentials
4. **Save** and set as **Active**

### 2. Configure Products

1. Go to **Inventory → Products**
2. Select products to sync with Ozon
3. Edit each product:
   - Go to **Ozon Integration** tab
   - Enable **Sync to Ozon**
   - Set **Ozon Offer ID** (usually same as SKU)
   - Configure **Ozon Category ID** if needed
4. **Save** the product

### 3. Test Integration

1. Go to **Ozon Integration → Manual Sync**
2. Select **Sync Products to Ozon**
3. Choose a few test products
4. Click **Start Synchronization**
5. Monitor results in **Integration Logs**

## Troubleshooting

### Common Issues:

1. **Module not found**:
   - Check addons_path in odoo.conf
   - Restart Odoo service
   - Update apps list

2. **Permission errors**:
   - Set correct file permissions
   - Ensure odoo user owns the files

3. **Python dependencies missing**:
   - Install requests and python-dotenv
   - Restart Odoo after installation

4. **API connection failed**:
   - Verify Ozon API credentials
   - Check internet connectivity
   - Test with Ozon API documentation

### Log Locations:

- **Odoo logs**: `/var/log/odoo/odoo.log`
- **Integration logs**: Available in Odoo UI under Monitoring
