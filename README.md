# Ozon Marketplace Integration for Odoo

A comprehensive Odoo addon that provides seamless integration between Odoo ERP and Ozon marketplace.

## Features

### 🛒 **Product Synchronization**
- Sync products from Odoo to Ozon marketplace
- Smart SKU-based product matching
- Batch processing for large catalogs
- Automatic price and description updates
- Category mapping and management

### 📊 **Stock Management**
- Real-time stock synchronization
- Multi-location inventory support
- Automatic stock updates on changes
- Stock level monitoring

### 🛍️ **Order Management**
- Automatic order import from Ozon
- Customer creation and matching
- Order status synchronization
- Delivery and payment tracking

### ⚙️ **Configuration & Monitoring**
- Easy API configuration
- Comprehensive logging
- Scheduled synchronization
- Manual sync wizards
- Integration dashboard

## Installation

1. **Download the addon** to your Odoo addons directory
2. **Update the apps list** in Odoo
3. **Install the addon** from Apps menu
4. **Configure your Ozon API credentials**

## Configuration

### 1. API Setup
1. Go to **Ozon Integration → Configuration → Ozon Settings**
2. Enter your Ozon API credentials:
   - Client ID
   - API Key
3. Test the connection
4. Configure sync settings

### 2. Product Setup
1. Go to **Inventory → Products**
2. Edit products you want to sync
3. Go to **Ozon Integration** tab
4. Enable **Sync to Ozon**
5. Set Ozon Offer ID (SKU)
6. Configure category and pricing

### 3. Scheduled Actions
The addon includes automatic cron jobs:
- **Product Sync**: Every 6 hours
- **Stock Sync**: Every 2 hours  
- **Order Import**: Every 30 minutes

## Usage

### Manual Synchronization
- Use **Ozon Integration → Manual Sync** for on-demand sync
- Select sync type: Products, Stock, Orders, or Full Sync
- Choose specific products or sync all enabled products

### Monitoring
- View logs at **Ozon Integration → Monitoring → Integration Logs**
- Check sync status on product forms
- Monitor statistics in configuration

### API Endpoints
The addon provides REST API endpoints:
- `/ozon/sync/products` - Sync products
- `/ozon/sync/stock` - Sync stock
- `/ozon/import/orders` - Import orders
- `/ozon/status` - Get integration status

## Security

The addon includes three security groups:
- **Ozon User**: View access to products and orders
- **Ozon Manager**: Sync operations and stock management
- **Ozon Administrator**: Full configuration access

## Requirements

- Odoo 17.0+
- Valid Ozon Seller account with API access
- Python packages: `requests`, `python-dotenv`

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Verify API credentials in Ozon Seller Portal
   - Check internet connection
   - Ensure API access is enabled

2. **Products Not Syncing**
   - Ensure products have SKU (Internal Reference)
   - Check Ozon sync is enabled on products
   - Verify category mapping

3. **Stock Not Updating**
   - Check stock locations configuration
   - Ensure products are synced to Ozon first
   - Verify inventory quantities

### Debug Mode
Enable debug logging by setting log level to DEBUG in configuration.

## Support

For technical support:
1. Check integration logs for detailed error information
2. Verify configuration settings
3. Test individual components separately
4. Contact support with log details

## License

This addon is licensed under LGPL-3.

## Changelog

### Version 1.0.0
- Initial release
- Product synchronization
- Stock synchronization  
- Order import
- Configuration management
- Logging and monitoring
- Security and access control
