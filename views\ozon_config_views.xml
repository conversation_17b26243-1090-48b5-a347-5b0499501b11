<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Ozon Configuration Form View -->
    <record id="view_ozon_config_form" model="ir.ui.view">
        <field name="name">ozon.config.form</field>
        <field name="model">ozon.config</field>
        <field name="arch" type="xml">
            <form string="Ozon Configuration">
                <header>
                    <button name="test_connection" string="Test Connection" type="object" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Configuration Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="API Configuration">
                            <field name="client_id" password="True"/>
                            <field name="api_key" password="True"/>
                            <field name="base_url"/>
                            <field name="active"/>
                        </group>
                        <group string="Status">
                            <field name="last_sync_date" readonly="1"/>
                            <field name="last_sync_status" readonly="1"/>
                            <field name="last_sync_message" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Integration Settings">
                            <group>
                                <group string="Batch Processing">
                                    <field name="batch_size"/>
                                    <field name="retry_attempts"/>
                                    <field name="retry_delay"/>
                                </group>
                                <group string="Auto Sync">
                                    <field name="auto_sync_products"/>
                                    <field name="auto_sync_stock"/>
                                    <field name="auto_import_orders"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Product Settings">
                            <group>
                                <group string="Default Values">
                                    <field name="default_category_id"/>
                                    <field name="currency_code"/>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Stock Settings">
                            <group>
                                <field name="stock_location_ids" widget="many2many_tags"/>
                            </group>
                        </page>
                        
                        <page string="Order Settings">
                            <group>
                                <field name="order_days_back"/>
                            </group>
                        </page>
                        
                        <page string="Statistics">
                            <group>
                                <group string="Sync Statistics">
                                    <field name="total_products_synced" readonly="1"/>
                                    <field name="total_orders_imported" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Ozon Configuration Tree View -->
    <record id="view_ozon_config_tree" model="ir.ui.view">
        <field name="name">ozon.config.tree</field>
        <field name="model">ozon.config</field>
        <field name="arch" type="xml">
            <tree string="Ozon Configurations">
                <field name="name"/>
                <field name="active"/>
                <field name="last_sync_date"/>
                <field name="last_sync_status"/>
                <field name="total_products_synced"/>
                <field name="total_orders_imported"/>
            </tree>
        </field>
    </record>

    <!-- Ozon Configuration Action -->
    <record id="action_ozon_config" model="ir.actions.act_window">
        <field name="name">Ozon Configuration</field>
        <field name="res_model">ozon.config</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Configure your Ozon marketplace integration
            </p>
            <p>
                Set up your Ozon API credentials and integration settings to start syncing products, stock, and orders.
            </p>
        </field>
    </record>
</odoo>
