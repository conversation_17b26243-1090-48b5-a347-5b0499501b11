#!/usr/bin/env python3
"""
Comprehensive Odoo-Ozon Integration System
==========================================

This module provides complete integration between Odoo ERP and Ozon marketplace:
- Product synchronization (Odoo -> Ozon)
- Stock synchronization (Odoo -> Ozon)
- Sales order synchronization (Ozon -> Odoo)
- Comprehensive logging and error handling

Author: Integration System
Date: 2025-09-03
"""

import odoolib
import requests
import os
import json
import logging
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from dotenv import load_dotenv
from xmlrpc.client import ProtocolError, Fault
import socket

# Load environment variables
load_dotenv()

@dataclass
class IntegrationConfig:
    """Configuration class for integration settings"""
    # Odoo Configuration
    odoo_hostname: str = "**************"
    odoo_database: str = "my-odoo-db"
    odoo_login: str = "<EMAIL>"
    odoo_password: str = "GoldLeaf@@786"
    odoo_port: int = 8069
    
    # Ozon Configuration
    ozon_client_id: str = os.getenv("OZON_CLIENT_ID", "")
    ozon_api_key: str = os.getenv("OZON_API_KEY", "")
    ozon_base_url: str = "https://api-seller.ozon.ru"
    
    # Integration Settings
    batch_size: int = 50
    retry_attempts: int = 3
    retry_delay: int = 5
    log_level: str = "INFO"
    log_file: str = "odoo_ozon_integration.log"

class IntegrationLogger:
    """Enhanced logging system for integration processes"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.setup_logging()
        
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)
        
        # Setup file handler with rotation
        log_filename = f"logs/{self.config.log_file}"
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('OdooOzonIntegration')
        
    def log_stage(self, stage: str, message: str, level: str = "INFO"):
        """Log integration stage with structured format"""
        formatted_message = f"[{stage}] {message}"
        getattr(self.logger, level.lower())(formatted_message)
        
    def log_product_sync(self, product_id: str, product_name: str, action: str, status: str, details: str = ""):
        """Log product synchronization details"""
        message = f"Product {action}: ID={product_id}, Name='{product_name}', Status={status}"
        if details:
            message += f", Details={details}"
        self.logger.info(f"[PRODUCT_SYNC] {message}")
        
    def log_stock_sync(self, product_id: str, odoo_qty: float, ozon_qty: float, status: str):
        """Log stock synchronization details"""
        message = f"Stock Sync: Product={product_id}, Odoo_Qty={odoo_qty}, Ozon_Qty={ozon_qty}, Status={status}"
        self.logger.info(f"[STOCK_SYNC] {message}")
        
    def log_order_sync(self, order_id: str, action: str, status: str, details: str = ""):
        """Log sales order synchronization details"""
        message = f"Order {action}: ID={order_id}, Status={status}"
        if details:
            message += f", Details={details}"
        self.logger.info(f"[ORDER_SYNC] {message}")
        
    def log_error(self, stage: str, error: Exception, context: str = ""):
        """Log errors with full traceback"""
        error_message = f"[{stage}] ERROR: {str(error)}"
        if context:
            error_message += f" | Context: {context}"
        error_message += f"\nTraceback: {traceback.format_exc()}"
        self.logger.error(error_message)

class OdooConnector:
    """Enhanced Odoo connection and data management"""
    
    def __init__(self, config: IntegrationConfig, logger: IntegrationLogger):
        self.config = config
        self.logger = logger
        self.connection = None
        
    def connect(self) -> bool:
        """Establish connection to Odoo"""
        try:
            self.logger.log_stage("ODOO_CONNECTION", "Attempting to connect to Odoo...")
            
            self.connection = odoolib.get_connection(
                hostname=self.config.odoo_hostname,
                database=self.config.odoo_database,
                login=self.config.odoo_login,
                password=self.config.odoo_password,
                port=self.config.odoo_port
            )
            
            self.logger.log_stage("ODOO_CONNECTION", "Successfully connected to Odoo")
            return True
            
        except Exception as e:
            self.logger.log_error("ODOO_CONNECTION", e)
            return False
            
    def get_products(self, limit: int = None) -> List[Dict]:
        """Fetch products from Odoo with comprehensive data"""
        if not self.connection:
            raise Exception("No Odoo connection available")
            
        try:
            self.logger.log_stage("ODOO_PRODUCTS", "Fetching products from Odoo...")
            
            product_model = self.connection.model('product.template')
            
            # Define fields to fetch
            fields = [
                'name', 'default_code', 'list_price', 'standard_price',
                'barcode', 'weight', 'volume', 'description_sale',
                'categ_id', 'uom_id', 'active', 'sale_ok',
                'image_1920', 'qty_available', 'virtual_available'
            ]
            
            # Search for active, saleable products
            domain = [('active', '=', True), ('sale_ok', '=', True)]
            
            if limit:
                products = product_model.search_read(domain, fields, limit=limit)
            else:
                products = product_model.search_read(domain, fields)
                
            self.logger.log_stage("ODOO_PRODUCTS", f"Fetched {len(products)} products from Odoo")
            return products
            
        except Exception as e:
            self.logger.log_error("ODOO_PRODUCTS", e)
            raise
            
    def get_stock_quantities(self, product_ids: List[int]) -> Dict[int, float]:
        """Get current stock quantities for products"""
        if not self.connection:
            raise Exception("No Odoo connection available")
            
        try:
            self.logger.log_stage("ODOO_STOCK", f"Fetching stock for {len(product_ids)} products...")
            
            stock_model = self.connection.model('stock.quant')
            
            stock_data = {}
            for product_id in product_ids:
                quants = stock_model.search_read(
                    [('product_id', '=', product_id), ('location_id.usage', '=', 'internal')],
                    ['quantity']
                )
                total_qty = sum(quant['quantity'] for quant in quants)
                stock_data[product_id] = total_qty
                
            self.logger.log_stage("ODOO_STOCK", f"Retrieved stock data for {len(stock_data)} products")
            return stock_data
            
        except Exception as e:
            self.logger.log_error("ODOO_STOCK", e)
            raise
            
    def create_sale_order(self, order_data: Dict) -> Optional[int]:
        """Create a sales order in Odoo from Ozon order data"""
        if not self.connection:
            raise Exception("No Odoo connection available")
            
        try:
            self.logger.log_stage("ODOO_ORDER_CREATE", f"Creating order for Ozon order {order_data.get('order_id')}")
            
            sale_order_model = self.connection.model('sale.order')
            
            # Prepare order data for Odoo
            odoo_order_data = {
                'partner_id': self._get_or_create_customer(order_data.get('customer_data', {})),
                'origin': f"OZON-{order_data.get('order_id')}",
                'client_order_ref': order_data.get('order_number'),
                'date_order': order_data.get('created_at'),
                'order_line': self._prepare_order_lines(order_data.get('products', []))
            }
            
            order_id = sale_order_model.create(odoo_order_data)
            
            self.logger.log_order_sync(
                order_data.get('order_id'),
                "CREATE",
                "SUCCESS",
                f"Created Odoo order ID: {order_id}"
            )
            
            return order_id
            
        except Exception as e:
            self.logger.log_error("ODOO_ORDER_CREATE", e, f"Order: {order_data.get('order_id')}")
            return None

    def _get_or_create_customer(self, customer_data: Dict) -> int:
        """Get or create customer in Odoo"""
        partner_model = self.connection.model('res.partner')

        # Search for existing customer
        email = customer_data.get('email', '')
        if email:
            existing = partner_model.search([('email', '=', email)])
            if existing:
                return existing[0]

        # Create new customer
        partner_data = {
            'name': customer_data.get('name', 'Ozon Customer'),
            'email': email,
            'phone': customer_data.get('phone', ''),
            'is_company': False,
            'customer_rank': 1,
        }

        return partner_model.create(partner_data)

    def _prepare_order_lines(self, products: List[Dict]) -> List[Tuple]:
        """Prepare order lines for Odoo sale order"""
        order_lines = []

        for product in products:
            # Find product in Odoo by SKU or barcode
            product_model = self.connection.model('product.template')
            odoo_products = product_model.search([
                '|',
                ('default_code', '=', product.get('offer_id')),
                ('barcode', '=', product.get('sku'))
            ])

            if odoo_products:
                order_lines.append((0, 0, {
                    'product_id': odoo_products[0],
                    'product_uom_qty': product.get('quantity', 1),
                    'price_unit': product.get('price', 0.0),
                }))

        return order_lines

class OzonConnector:
    """Enhanced Ozon API connector with comprehensive functionality"""

    def __init__(self, config: IntegrationConfig, logger: IntegrationLogger):
        self.config = config
        self.logger = logger
        self.headers = {
            "Client-Id": config.ozon_client_id,
            "Api-Key": config.ozon_api_key,
            "Content-Type": "application/json"
        }

    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Optional[Dict]:
        """Make HTTP request to Ozon API with retry logic"""
        url = f"{self.config.ozon_base_url}{endpoint}"

        for attempt in range(self.config.retry_attempts):
            try:
                if method.upper() == 'GET':
                    response = requests.get(url, headers=self.headers, params=data)
                else:
                    response = requests.post(url, headers=self.headers, json=data)

                response.raise_for_status()
                return response.json()

            except requests.exceptions.RequestException as e:
                self.logger.log_error("OZON_API", e, f"Attempt {attempt + 1}/{self.config.retry_attempts}")
                if attempt < self.config.retry_attempts - 1:
                    time.sleep(self.config.retry_delay)
                else:
                    raise

        return None

    def get_products(self, limit: int = 100) -> List[Dict]:
        """Fetch products from Ozon"""
        try:
            self.logger.log_stage("OZON_PRODUCTS", "Fetching products from Ozon...")

            payload = {
                "filter": {},
                "limit": limit,
                "last_id": ""
            }

            response = self._make_request("POST", "/v2/product/list", payload)

            if response and 'result' in response:
                products = response['result'].get('items', [])
                self.logger.log_stage("OZON_PRODUCTS", f"Fetched {len(products)} products from Ozon")
                return products
            else:
                self.logger.log_stage("OZON_PRODUCTS", "No products found in Ozon response", "WARNING")
                return []

        except Exception as e:
            self.logger.log_error("OZON_PRODUCTS", e)
            raise

    def create_or_update_product(self, product_data: Dict) -> Optional[str]:
        """Create or update product in Ozon"""
        try:
            self.logger.log_stage("OZON_PRODUCT_SYNC", f"Syncing product: {product_data.get('name')}")

            # Check if product exists
            existing_product = self._find_product_by_sku(product_data.get('default_code'))

            if existing_product:
                return self._update_product(existing_product['product_id'], product_data)
            else:
                return self._create_product(product_data)

        except Exception as e:
            self.logger.log_error("OZON_PRODUCT_SYNC", e, f"Product: {product_data.get('name')}")
            return None

    def _find_product_by_sku(self, sku: str) -> Optional[Dict]:
        """Find product in Ozon by SKU"""
        if not sku:
            return None

        try:
            payload = {
                "filter": {
                    "offer_id": [sku]
                },
                "limit": 1,
                "last_id": ""
            }

            response = self._make_request("POST", "/v2/product/list", payload)

            if response and 'result' in response:
                items = response['result'].get('items', [])
                return items[0] if items else None

        except Exception as e:
            self.logger.log_error("OZON_PRODUCT_SEARCH", e, f"SKU: {sku}")

        return None

    def _create_product(self, product_data: Dict) -> Optional[str]:
        """Create new product in Ozon"""
        try:
            # Map Odoo product data to Ozon format
            ozon_product = self._map_odoo_to_ozon_product(product_data)

            payload = {"items": [ozon_product]}

            response = self._make_request("POST", "/v3/product/import", payload)

            if response and 'result' in response:
                task_id = response['result'].get('task_id')
                self.logger.log_product_sync(
                    product_data.get('default_code', 'N/A'),
                    product_data.get('name', 'N/A'),
                    "CREATE",
                    "SUBMITTED",
                    f"Task ID: {task_id}"
                )
                return task_id
            else:
                self.logger.log_product_sync(
                    product_data.get('default_code', 'N/A'),
                    product_data.get('name', 'N/A'),
                    "CREATE",
                    "FAILED",
                    "No task ID returned"
                )
                return None

        except Exception as e:
            self.logger.log_error("OZON_PRODUCT_CREATE", e, f"Product: {product_data.get('name')}")
            return None

    def _update_product(self, product_id: str, product_data: Dict) -> Optional[str]:
        """Update existing product in Ozon"""
        try:
            # Prepare update data
            update_data = {
                "product_id": product_id,
                "price": str(product_data.get('list_price', 0)),
                "old_price": str(product_data.get('list_price', 0) * 1.1),  # 10% markup for old price
                "currency_code": "RUB"
            }

            response = self._make_request("POST", "/v1/product/import/prices", {"prices": [update_data]})

            if response:
                self.logger.log_product_sync(
                    product_data.get('default_code', 'N/A'),
                    product_data.get('name', 'N/A'),
                    "UPDATE",
                    "SUCCESS",
                    f"Updated product {product_id}"
                )
                return product_id
            else:
                self.logger.log_product_sync(
                    product_data.get('default_code', 'N/A'),
                    product_data.get('name', 'N/A'),
                    "UPDATE",
                    "FAILED",
                    f"Failed to update product {product_id}"
                )
                return None

        except Exception as e:
            self.logger.log_error("OZON_PRODUCT_UPDATE", e, f"Product ID: {product_id}")
            return None

    def _map_odoo_to_ozon_product(self, product_data: Dict) -> Dict:
        """Map Odoo product data to Ozon product format"""
        return {
            "attributes": [
                {
                    "complex_id": 0,
                    "id": 9048,  # Name attribute
                    "values": [{"value": product_data.get('name', '')}]
                }
            ],
            "barcode": product_data.get('barcode', ''),
            "description_category_id": 17028922,  # Default category
            "currency_code": "RUB",
            "depth": int(product_data.get('volume', 10)),
            "dimension_unit": "mm",
            "height": 100,  # Default dimensions
            "width": 100,
            "images": [],
            "name": product_data.get('name', ''),
            "offer_id": product_data.get('default_code', ''),
            "price": str(product_data.get('list_price', 0)),
            "old_price": str(product_data.get('list_price', 0) * 1.1),
            "vat": "0.1",
            "weight": int(product_data.get('weight', 100)),
            "weight_unit": "g"
        }

    def update_stock(self, product_stocks: Dict[str, float]) -> bool:
        """Update stock quantities in Ozon"""
        try:
            self.logger.log_stage("OZON_STOCK_UPDATE", f"Updating stock for {len(product_stocks)} products")

            stocks_data = []
            for offer_id, quantity in product_stocks.items():
                stocks_data.append({
                    "offer_id": offer_id,
                    "stock": int(max(0, quantity))  # Ensure non-negative stock
                })

            payload = {"stocks": stocks_data}

            response = self._make_request("POST", "/v1/product/import/stocks", payload)

            if response:
                self.logger.log_stage("OZON_STOCK_UPDATE", "Stock update submitted successfully")

                # Log individual stock updates
                for offer_id, quantity in product_stocks.items():
                    self.logger.log_stock_sync(offer_id, quantity, quantity, "UPDATED")

                return True
            else:
                self.logger.log_stage("OZON_STOCK_UPDATE", "Failed to update stock", "ERROR")
                return False

        except Exception as e:
            self.logger.log_error("OZON_STOCK_UPDATE", e)
            return False

    def get_orders(self, since_date: datetime = None) -> List[Dict]:
        """Fetch orders from Ozon"""
        try:
            if not since_date:
                since_date = datetime.now() - timedelta(days=7)  # Last 7 days by default

            self.logger.log_stage("OZON_ORDERS", f"Fetching orders since {since_date}")

            payload = {
                "filter": {
                    "since": since_date.isoformat(),
                    "status": "delivered"  # Only get delivered orders
                },
                "limit": 100,
                "offset": 0
            }

            response = self._make_request("POST", "/v3/posting/fbs/list", payload)

            if response and 'result' in response:
                orders = response['result'].get('postings', [])
                self.logger.log_stage("OZON_ORDERS", f"Fetched {len(orders)} orders from Ozon")
                return orders
            else:
                self.logger.log_stage("OZON_ORDERS", "No orders found in Ozon response", "WARNING")
                return []

        except Exception as e:
            self.logger.log_error("OZON_ORDERS", e)
            raise

class OdooOzonIntegrator:
    """Main integration orchestrator"""

    def __init__(self, config: IntegrationConfig = None):
        self.config = config or IntegrationConfig()
        self.logger = IntegrationLogger(self.config)
        self.odoo = OdooConnector(self.config, self.logger)
        self.ozon = OzonConnector(self.config, self.logger)

    def validate_configuration(self) -> bool:
        """Validate all required configuration parameters"""
        self.logger.log_stage("CONFIG_VALIDATION", "Validating configuration...")

        missing_configs = []

        if not self.config.ozon_client_id:
            missing_configs.append("OZON_CLIENT_ID")
        if not self.config.ozon_api_key:
            missing_configs.append("OZON_API_KEY")

        if missing_configs:
            self.logger.log_stage("CONFIG_VALIDATION", f"Missing configurations: {', '.join(missing_configs)}", "ERROR")
            return False

        self.logger.log_stage("CONFIG_VALIDATION", "Configuration validation successful")
        return True

    def sync_products_to_ozon(self) -> Dict[str, int]:
        """Synchronize all products from Odoo to Ozon"""
        self.logger.log_stage("PRODUCT_SYNC_START", "Starting product synchronization from Odoo to Ozon")

        results = {"success": 0, "failed": 0, "skipped": 0}

        try:
            # Connect to Odoo
            if not self.odoo.connect():
                raise Exception("Failed to connect to Odoo")

            # Fetch products from Odoo
            odoo_products = self.odoo.get_products()

            self.logger.log_stage("PRODUCT_SYNC_PROGRESS", f"Processing {len(odoo_products)} products")

            # Process products in batches
            for i in range(0, len(odoo_products), self.config.batch_size):
                batch = odoo_products[i:i + self.config.batch_size]

                self.logger.log_stage("PRODUCT_SYNC_BATCH", f"Processing batch {i//self.config.batch_size + 1}")

                for product in batch:
                    try:
                        # Skip products without SKU
                        if not product.get('default_code'):
                            self.logger.log_product_sync(
                                "N/A",
                                product.get('name', 'N/A'),
                                "SYNC",
                                "SKIPPED",
                                "No SKU/default_code"
                            )
                            results["skipped"] += 1
                            continue

                        # Sync product to Ozon
                        task_id = self.ozon.create_or_update_product(product)

                        if task_id:
                            results["success"] += 1
                        else:
                            results["failed"] += 1

                    except Exception as e:
                        self.logger.log_error("PRODUCT_SYNC_ITEM", e, f"Product: {product.get('name')}")
                        results["failed"] += 1

                # Small delay between batches
                time.sleep(1)

        except Exception as e:
            self.logger.log_error("PRODUCT_SYNC", e)

        self.logger.log_stage("PRODUCT_SYNC_COMPLETE",
                            f"Product sync completed. Success: {results['success']}, "
                            f"Failed: {results['failed']}, Skipped: {results['skipped']}")

        return results

    def sync_stock_to_ozon(self) -> Dict[str, int]:
        """Synchronize stock quantities from Odoo to Ozon"""
        self.logger.log_stage("STOCK_SYNC_START", "Starting stock synchronization from Odoo to Ozon")

        results = {"success": 0, "failed": 0}

        try:
            # Connect to Odoo
            if not self.odoo.connect():
                raise Exception("Failed to connect to Odoo")

            # Get products with SKUs
            odoo_products = self.odoo.get_products()
            products_with_sku = [p for p in odoo_products if p.get('default_code')]

            if not products_with_sku:
                self.logger.log_stage("STOCK_SYNC", "No products with SKU found", "WARNING")
                return results

            # Get stock quantities
            product_ids = [p['id'] for p in products_with_sku]
            stock_data = self.odoo.get_stock_quantities(product_ids)

            # Prepare stock data for Ozon (map by SKU)
            ozon_stock_data = {}
            for product in products_with_sku:
                product_id = product['id']
                sku = product['default_code']
                quantity = stock_data.get(product_id, 0)
                ozon_stock_data[sku] = quantity

            # Update stock in Ozon
            if self.ozon.update_stock(ozon_stock_data):
                results["success"] = len(ozon_stock_data)
                self.logger.log_stage("STOCK_SYNC_COMPLETE", f"Successfully updated stock for {results['success']} products")
            else:
                results["failed"] = len(ozon_stock_data)
                self.logger.log_stage("STOCK_SYNC_COMPLETE", f"Failed to update stock for {results['failed']} products", "ERROR")

        except Exception as e:
            self.logger.log_error("STOCK_SYNC", e)
            results["failed"] = len(ozon_stock_data) if 'ozon_stock_data' in locals() else 0

        return results

    def sync_orders_from_ozon(self, days_back: int = 7) -> Dict[str, int]:
        """Synchronize sales orders from Ozon to Odoo"""
        self.logger.log_stage("ORDER_SYNC_START", f"Starting order synchronization from Ozon to Odoo (last {days_back} days)")

        results = {"success": 0, "failed": 0, "skipped": 0}

        try:
            # Connect to Odoo
            if not self.odoo.connect():
                raise Exception("Failed to connect to Odoo")

            # Fetch orders from Ozon
            since_date = datetime.now() - timedelta(days=days_back)
            ozon_orders = self.ozon.get_orders(since_date)

            self.logger.log_stage("ORDER_SYNC_PROGRESS", f"Processing {len(ozon_orders)} orders")

            for order in ozon_orders:
                try:
                    order_id = order.get('posting_number', 'N/A')

                    # Check if order already exists in Odoo
                    if self._order_exists_in_odoo(order_id):
                        self.logger.log_order_sync(order_id, "SYNC", "SKIPPED", "Order already exists")
                        results["skipped"] += 1
                        continue

                    # Prepare order data
                    order_data = self._prepare_order_data(order)

                    # Create order in Odoo
                    odoo_order_id = self.odoo.create_sale_order(order_data)

                    if odoo_order_id:
                        results["success"] += 1
                    else:
                        results["failed"] += 1

                except Exception as e:
                    self.logger.log_error("ORDER_SYNC_ITEM", e, f"Order: {order.get('posting_number')}")
                    results["failed"] += 1

        except Exception as e:
            self.logger.log_error("ORDER_SYNC", e)

        self.logger.log_stage("ORDER_SYNC_COMPLETE",
                            f"Order sync completed. Success: {results['success']}, "
                            f"Failed: {results['failed']}, Skipped: {results['skipped']}")

        return results

    def _order_exists_in_odoo(self, ozon_order_id: str) -> bool:
        """Check if order already exists in Odoo"""
        try:
            sale_order_model = self.odoo.connection.model('sale.order')
            existing_orders = sale_order_model.search([('origin', '=', f'OZON-{ozon_order_id}')])
            return len(existing_orders) > 0
        except:
            return False

    def _prepare_order_data(self, ozon_order: Dict) -> Dict:
        """Prepare Ozon order data for Odoo"""
        return {
            'order_id': ozon_order.get('posting_number'),
            'order_number': ozon_order.get('order_number'),
            'created_at': ozon_order.get('created_at'),
            'customer_data': {
                'name': ozon_order.get('addressee', {}).get('name', 'Ozon Customer'),
                'email': '',  # Ozon doesn't provide customer email
                'phone': ozon_order.get('addressee', {}).get('phone', '')
            },
            'products': ozon_order.get('products', [])
        }

    def run_full_integration(self) -> Dict[str, Dict[str, int]]:
        """Run complete integration process"""
        self.logger.log_stage("INTEGRATION_START", "Starting full Odoo-Ozon integration")

        start_time = datetime.now()
        results = {}

        try:
            # Validate configuration
            if not self.validate_configuration():
                raise Exception("Configuration validation failed")

            # 1. Sync products from Odoo to Ozon
            self.logger.log_stage("INTEGRATION_PHASE", "Phase 1: Product Synchronization")
            results['products'] = self.sync_products_to_ozon()

            # 2. Sync stock from Odoo to Ozon
            self.logger.log_stage("INTEGRATION_PHASE", "Phase 2: Stock Synchronization")
            results['stock'] = self.sync_stock_to_ozon()

            # 3. Sync orders from Ozon to Odoo
            self.logger.log_stage("INTEGRATION_PHASE", "Phase 3: Order Synchronization")
            results['orders'] = self.sync_orders_from_ozon()

        except Exception as e:
            self.logger.log_error("INTEGRATION", e)

        end_time = datetime.now()
        duration = end_time - start_time

        self.logger.log_stage("INTEGRATION_COMPLETE",
                            f"Full integration completed in {duration.total_seconds():.2f} seconds")

        # Log summary
        self._log_integration_summary(results)

        return results

    def _log_integration_summary(self, results: Dict[str, Dict[str, int]]):
        """Log comprehensive integration summary"""
        self.logger.log_stage("INTEGRATION_SUMMARY", "=== INTEGRATION SUMMARY ===")

        for phase, phase_results in results.items():
            self.logger.log_stage("INTEGRATION_SUMMARY", f"{phase.upper()}:")
            for status, count in phase_results.items():
                self.logger.log_stage("INTEGRATION_SUMMARY", f"  {status}: {count}")

        # Calculate totals
        total_success = sum(phase.get('success', 0) for phase in results.values())
        total_failed = sum(phase.get('failed', 0) for phase in results.values())
        total_skipped = sum(phase.get('skipped', 0) for phase in results.values())

        self.logger.log_stage("INTEGRATION_SUMMARY", f"TOTAL SUCCESS: {total_success}")
        self.logger.log_stage("INTEGRATION_SUMMARY", f"TOTAL FAILED: {total_failed}")
        self.logger.log_stage("INTEGRATION_SUMMARY", f"TOTAL SKIPPED: {total_skipped}")
        self.logger.log_stage("INTEGRATION_SUMMARY", "=== END SUMMARY ===")

def main():
    """Main execution function"""
    print("🚀 Starting Odoo-Ozon Integration System")
    print("=" * 50)

    try:
        # Initialize integrator
        integrator = OdooOzonIntegrator()

        # Run full integration
        results = integrator.run_full_integration()

        print("\n✅ Integration completed successfully!")
        print("📊 Results summary:")

        for phase, phase_results in results.items():
            print(f"\n{phase.upper()}:")
            for status, count in phase_results.items():
                print(f"  {status}: {count}")

        print(f"\n📝 Detailed logs available in: logs/{integrator.config.log_file}")

    except Exception as e:
        print(f"\n❌ Integration failed: {str(e)}")
        print("📝 Check logs for detailed error information")

    print("\n" + "=" * 50)
    print("🏁 Integration process finished")

if __name__ == "__main__":
    main()
