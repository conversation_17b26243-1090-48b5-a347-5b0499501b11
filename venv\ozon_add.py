import requests
import os
import json
from dotenv import load_dotenv
import sys

# It's good practice to load secrets from environment variables
# instead of hardcoding them in the script.
load_dotenv()

# Your credentials (from Ozon Seller Portal)
CLIENT_ID = os.getenv("OZON_CLIENT_ID")
API_KEY = os.getenv("OZON_API_KEY")

# Validate that credentials were loaded successfully before making the API call
if not all([CLIENT_ID, API_KEY]):
    print("Error: Missing OZON_CLIENT_ID or OZON_API_KEY.")
    print("Please ensure these are set in your .env file and the file is in the correct directory.")
    sys.exit(1)

# API endpoint for product list
url = "https://api-seller.ozon.ru/v3/product/import"  # Correct endpoint for creating products

headers = {
    "Client-Id": CLIENT_ID,
    "Api-Key": API_KEY,
    "Content-Type": "application/json"
}

payload = {
    "items": [
        {
            "attributes": [
                {
                    "complex_id": 0,
                    "id": 5076,
                    "values": [
                        {
                            "dictionary_value_id": 971082156,
                            "value": "Speaker stand"
                        }
                    ]
                },
                {
                    "complex_id": 0,
                    "id": 9048,
                    "values": [
                        {
                            "value": "X3 NFC screen protector set. Dark cotton"
                        }
                    ]
                },
                {
                    "complex_id": 0,
                    "id": 8229,
                    "values": [
                        {
                            "dictionary_value_id": 95911,
                            "value": "X3 NFC screen protector set. Dark cotton"
                        }
                    ]
                },
                {
                    "complex_id": 0,
                    "id": 85,
                    "values": [
                        {
                            "dictionary_value_id": 5060050,
                            "value": "Samsung"
                        }
                    ]
                },
                {
                    "complex_id": 0,
                    "id": 10096,
                    "values": [
                        {
                            "dictionary_value_id": 61576,
                            "value": "grey"
                        }
                    ]
                }
            ],
            "barcode": "112772873170",
            "description_category_id": 17028922,
            "color_image": "",
            "complex_attributes": [],
            "currency_code": "RUB",
            "depth": 10,
            "dimension_unit": "mm",
            "height": 250,
            "images": [],
            "images360": [],
            "name": "X3 NFC screen protector set. Dark cotton",
            "offer_id": "143210608",
            "old_price": "1100",
            "pdf_list": [],
            "price": "1000",
            "primary_image": "",
            "promotions": [
                {
                    "operation": "UNKNOWN",
                    "type": "REVIEWS_PROMO"
                }
            ],
            "type_id": 91565,
            "vat": "0.1",
            "weight": 100,
            "weight_unit": "g",
            "width": 150
        }
    ]
}

try:
    response = requests.post(url, headers=headers, json=payload)
    # This will raise an exception for HTTP error codes (4xx or 5xx)
    response.raise_for_status()

    # The response for a successful import is a task_id
    result = response.json()
    task_id = result.get('result', {}).get('task_id')

    if task_id:
        print(f"Successfully submitted product import task. Task ID: {task_id}")
        print("You can check the status of this task using the /v1/product/import/info endpoint.")
    else:
        print("Request was successful, but no task ID was returned. Full response:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

except requests.exceptions.HTTPError as http_err:
    print(f"HTTP error occurred: {http_err}")
    print(f"Status Code: {response.status_code}")
    print(f"Response Body: {response.text}")  # Print the raw text to see the actual error
except requests.exceptions.RequestException as err:
    print(f"An error occurred during the request: {err}")
