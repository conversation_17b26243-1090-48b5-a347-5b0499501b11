<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Ozon Integration Log Tree View -->
    <record id="view_ozon_integration_log_tree" model="ir.ui.view">
        <field name="name">ozon.integration.log.tree</field>
        <field name="model">ozon.integration.log</field>
        <field name="arch" type="xml">
            <tree string="Integration Logs" decoration-success="status=='success'" 
                  decoration-warning="status=='warning'" decoration-danger="status=='error'">
                <field name="create_date"/>
                <field name="operation_type"/>
                <field name="status"/>
                <field name="duration"/>
                <field name="success_count"/>
                <field name="error_count"/>
                <field name="skipped_count"/>
                <field name="config_id"/>
            </tree>
        </field>
    </record>

    <!-- Ozon Integration Log Form View -->
    <record id="view_ozon_integration_log_form" model="ir.ui.view">
        <field name="name">ozon.integration.log.form</field>
        <field name="model">ozon.integration.log</field>
        <field name="arch" type="xml">
            <form string="Integration Log" create="false" edit="false">
                <sheet>
                    <group>
                        <group>
                            <field name="operation_type"/>
                            <field name="status"/>
                            <field name="config_id"/>
                        </group>
                        <group>
                            <field name="start_time"/>
                            <field name="end_time"/>
                            <field name="duration"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="Results">
                            <field name="success_count"/>
                            <field name="error_count"/>
                            <field name="skipped_count"/>
                        </group>
                    </group>
                    
                    <group string="Details">
                        <field name="message" nolabel="1"/>
                    </group>
                    
                    <group string="Error Details" attrs="{'invisible': [('error_details', '=', False)]}">
                        <field name="error_details" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Ozon Product Sync Tree View -->
    <record id="view_ozon_product_sync_tree" model="ir.ui.view">
        <field name="name">ozon.product.sync.tree</field>
        <field name="model">ozon.product.sync</field>
        <field name="arch" type="xml">
            <tree string="Product Sync" decoration-success="sync_status=='synced'" 
                  decoration-warning="sync_status=='pending'" decoration-danger="sync_status=='error'">
                <field name="product_id"/>
                <field name="ozon_offer_id"/>
                <field name="sync_status"/>
                <field name="last_sync_date"/>
                <field name="ozon_price"/>
                <field name="ozon_stock"/>
                <button name="sync_to_ozon" string="Sync Now" type="object" icon="fa-refresh"/>
            </tree>
        </field>
    </record>

    <!-- Ozon Product Sync Form View -->
    <record id="view_ozon_product_sync_form" model="ir.ui.view">
        <field name="name">ozon.product.sync.form</field>
        <field name="model">ozon.product.sync</field>
        <field name="arch" type="xml">
            <form string="Product Sync">
                <header>
                    <button name="sync_to_ozon" string="Sync to Ozon" type="object" class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="product_id"/>
                            <field name="ozon_offer_id"/>
                            <field name="ozon_product_id"/>
                        </group>
                        <group>
                            <field name="sync_status"/>
                            <field name="last_sync_date"/>
                            <field name="active"/>
                        </group>
                    </group>
                    
                    <group>
                        <group string="Ozon Settings">
                            <field name="ozon_category_id"/>
                            <field name="ozon_price"/>
                            <field name="ozon_stock"/>
                        </group>
                    </group>
                    
                    <group string="Last Error" attrs="{'invisible': [('last_error', '=', False)]}">
                        <field name="last_error" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Ozon Stock Sync Tree View -->
    <record id="view_ozon_stock_sync_tree" model="ir.ui.view">
        <field name="name">ozon.stock.sync.tree</field>
        <field name="model">ozon.stock.sync</field>
        <field name="arch" type="xml">
            <tree string="Stock Sync" decoration-success="sync_status=='synced'" 
                  decoration-warning="sync_status=='pending'" decoration-danger="sync_status=='error'">
                <field name="product_id"/>
                <field name="odoo_stock"/>
                <field name="ozon_stock"/>
                <field name="sync_status"/>
                <field name="last_sync_date"/>
                <button name="sync_stock_to_ozon" string="Sync Stock" type="object" icon="fa-refresh"/>
            </tree>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_ozon_integration_log" model="ir.actions.act_window">
        <field name="name">Integration Logs</field>
        <field name="res_model">ozon.integration.log</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_group_by_operation_type': 1}</field>
    </record>

    <record id="action_ozon_product_sync" model="ir.actions.act_window">
        <field name="name">Product Sync</field>
        <field name="res_model">ozon.product.sync</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_group_by_status': 1}</field>
    </record>

    <record id="action_ozon_stock_sync" model="ir.actions.act_window">
        <field name="name">Stock Sync</field>
        <field name="res_model">ozon.stock.sync</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_group_by_status': 1}</field>
    </record>
</odoo>
