# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Ozon Integration Fields
    ozon_sync_enabled = fields.Boolean(
        string='Sync to Ozon',
        default=True,
        help='Enable synchronization of this product to Ozon'
    )
    
    ozon_product_id = fields.Char(
        string='Ozon Product ID',
        readonly=True,
        help='Internal Ozon product identifier'
    )
    
    ozon_offer_id = fields.Char(
        string='Ozon Offer ID',
        help='Ozon offer identifier (usually SKU)'
    )
    
    ozon_category_id = fields.Integer(
        string='Ozon Category ID',
        help='Ozon marketplace category ID'
    )
    
    ozon_sync_status = fields.Selection([
        ('not_synced', 'Not Synced'),
        ('pending', 'Pending'),
        ('synced', 'Synced'),
        ('error', 'Error')
    ], string='Ozon Sync Status', default='not_synced', readonly=True)
    
    ozon_last_sync_date = fields.Datetime(
        string='Last Ozon Sync',
        readonly=True
    )
    
    ozon_last_error = fields.Text(
        string='Last Ozon Error',
        readonly=True
    )
    
    # Ozon specific pricing
    ozon_price = fields.Float(
        string='Ozon Price',
        help='Price on Ozon marketplace'
    )
    
    ozon_old_price = fields.Float(
        string='Ozon Old Price',
        help='Old price on Ozon (for discounts)'
    )
    
    # Stock tracking
    ozon_stock_qty = fields.Integer(
        string='Ozon Stock',
        readonly=True,
        help='Current stock quantity on Ozon'
    )
    
    @api.model
    def create(self, vals):
        """Override create to set default Ozon offer ID"""
        product = super().create(vals)
        
        # Set Ozon offer ID to default_code if not provided
        if product.ozon_sync_enabled and not product.ozon_offer_id and product.default_code:
            product.ozon_offer_id = product.default_code
            
        return product
    
    def write(self, vals):
        """Override write to handle Ozon sync updates"""
        result = super().write(vals)
        
        # If price or stock changed, mark for sync
        sync_fields = ['list_price', 'standard_price', 'name', 'description_sale']
        if any(field in vals for field in sync_fields):
            for product in self.filtered('ozon_sync_enabled'):
                if product.ozon_sync_status == 'synced':
                    product.ozon_sync_status = 'pending'
                    
        return result
    
    def action_sync_to_ozon(self):
        """Manual sync action for products"""
        self.ensure_one()
        
        if not self.ozon_sync_enabled:
            raise UserError(_('Ozon sync is not enabled for this product'))
            
        if not self.default_code:
            raise UserError(_('Product must have a SKU (Internal Reference) to sync to Ozon'))
        
        # Create or update sync record
        sync_record = self.env['ozon.product.sync'].search([
            ('product_id', '=', self.id)
        ], limit=1)
        
        if not sync_record:
            sync_record = self.env['ozon.product.sync'].create({
                'product_id': self.id,
                'ozon_offer_id': self.ozon_offer_id or self.default_code,
                'ozon_category_id': self.ozon_category_id or 17028922
            })
        
        # Perform sync
        self.ozon_sync_status = 'pending'
        sync_record.sync_to_ozon()
        
        # Update product status based on sync result
        if sync_record.sync_status == 'synced':
            self.ozon_sync_status = 'synced'
            self.ozon_last_sync_date = fields.Datetime.now()
            self.ozon_last_error = False
        else:
            self.ozon_sync_status = 'error'
            self.ozon_last_error = sync_record.last_error
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sync Complete'),
                'message': _('Product sync to Ozon completed with status: %s') % self.ozon_sync_status,
                'type': 'success' if self.ozon_sync_status == 'synced' else 'warning',
            }
        }
    
    def action_sync_stock_to_ozon(self):
        """Manual stock sync action"""
        self.ensure_one()
        
        if not self.ozon_sync_enabled:
            raise UserError(_('Ozon sync is not enabled for this product'))
            
        if not self.default_code:
            raise UserError(_('Product must have a SKU to sync stock to Ozon'))
        
        # Create or update stock sync record
        stock_sync = self.env['ozon.stock.sync'].search([
            ('product_id', '=', self.id)
        ], limit=1)
        
        if not stock_sync:
            stock_sync = self.env['ozon.stock.sync'].create({
                'product_id': self.id
            })
        
        # Perform stock sync
        stock_sync.sync_stock_to_ozon()
        
        # Update product stock info
        if stock_sync.sync_status == 'synced':
            self.ozon_stock_qty = stock_sync.ozon_stock
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Stock Sync Complete'),
                'message': _('Stock sync to Ozon completed with status: %s') % stock_sync.sync_status,
                'type': 'success' if stock_sync.sync_status == 'synced' else 'warning',
            }
        }
    
    def action_view_ozon_sync_logs(self):
        """View sync logs for this product"""
        self.ensure_one()
        
        # Get sync records
        product_syncs = self.env['ozon.product.sync'].search([('product_id', '=', self.id)])
        stock_syncs = self.env['ozon.stock.sync'].search([('product_id', '=', self.id)])
        
        return {
            'name': _('Ozon Sync Records'),
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'res_model': 'ozon.product.sync',
            'domain': [('product_id', '=', self.id)],
            'context': {'default_product_id': self.id}
        }
    
    @api.model
    def cron_sync_products_to_ozon(self):
        """Cron job to sync products to Ozon"""
        config = self.env['ozon.config'].search([('active', '=', True)], limit=1)
        if not config or not config.auto_sync_products:
            return
        
        # Get products that need syncing
        products = self.search([
            ('ozon_sync_enabled', '=', True),
            ('ozon_sync_status', 'in', ['not_synced', 'pending', 'error']),
            ('default_code', '!=', False)
        ], limit=config.batch_size)
        
        _logger.info(f"Starting automatic sync for {len(products)} products")
        
        for product in products:
            try:
                product.action_sync_to_ozon()
            except Exception as e:
                _logger.error(f"Error syncing product {product.name}: {str(e)}")
                product.ozon_sync_status = 'error'
                product.ozon_last_error = str(e)
    
    @api.model
    def cron_sync_stock_to_ozon(self):
        """Cron job to sync stock to Ozon"""
        config = self.env['ozon.config'].search([('active', '=', True)], limit=1)
        if not config or not config.auto_sync_stock:
            return
        
        # Get products with Ozon sync enabled
        products = self.search([
            ('ozon_sync_enabled', '=', True),
            ('ozon_sync_status', '=', 'synced'),
            ('default_code', '!=', False)
        ], limit=config.batch_size)
        
        _logger.info(f"Starting automatic stock sync for {len(products)} products")
        
        for product in products:
            try:
                product.action_sync_stock_to_ozon()
            except Exception as e:
                _logger.error(f"Error syncing stock for product {product.name}: {str(e)}")


class ProductProduct(models.Model):
    _inherit = 'product.product'
    
    def action_sync_to_ozon(self):
        """Redirect to template sync action"""
        return self.product_tmpl_id.action_sync_to_ozon()
    
    def action_sync_stock_to_ozon(self):
        """Redirect to template stock sync action"""
        return self.product_tmpl_id.action_sync_stock_to_ozon()
