<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Default Ozon Configuration -->
    <record id="default_ozon_config" model="ozon.config">
        <field name="name">Default Ozon Configuration</field>
        <field name="client_id">your_client_id_here</field>
        <field name="api_key">your_api_key_here</field>
        <field name="base_url">https://api-seller.ozon.ru</field>
        <field name="batch_size">50</field>
        <field name="retry_attempts">3</field>
        <field name="retry_delay">5</field>
        <field name="auto_sync_products">True</field>
        <field name="auto_sync_stock">True</field>
        <field name="auto_import_orders">True</field>
        <field name="default_category_id">17028922</field>
        <field name="currency_code">RUB</field>
        <field name="order_days_back">7</field>
        <field name="active">False</field>
    </record>

    <!-- Server Actions for Manual Sync -->
    <record id="action_server_sync_products" model="ir.actions.server">
        <field name="name">Sync Selected Products to Ozon</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="binding_model_id" ref="product.model_product_template"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
for record in records:
    if record.ozon_sync_enabled:
        record.action_sync_to_ozon()
        </field>
    </record>

    <record id="action_server_sync_stock" model="ir.actions.server">
        <field name="name">Sync Selected Stock to Ozon</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="binding_model_id" ref="product.model_product_template"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
for record in records:
    if record.ozon_sync_enabled and record.ozon_sync_status == 'synced':
        record.action_sync_stock_to_ozon()
        </field>
    </record>

    <!-- Email Templates -->
    <record id="email_template_sync_error" model="mail.template">
        <field name="name">Ozon Sync Error Notification</field>
        <field name="model_id" ref="model_ozon_integration_log"/>
        <field name="subject">Ozon Integration Error - ${object.operation_type}</field>
        <field name="body_html" type="html">
            <div style="margin: 0px; padding: 0px;">
                <p>Hello,</p>
                <p>An error occurred during Ozon integration:</p>
                <ul>
                    <li><strong>Operation:</strong> ${object.operation_type}</li>
                    <li><strong>Status:</strong> ${object.status}</li>
                    <li><strong>Start Time:</strong> ${object.start_time}</li>
                    <li><strong>Duration:</strong> ${object.duration} seconds</li>
                    <li><strong>Success Count:</strong> ${object.success_count}</li>
                    <li><strong>Error Count:</strong> ${object.error_count}</li>
                </ul>
                
                % if object.message:
                <p><strong>Message:</strong></p>
                <p>${object.message}</p>
                % endif
                
                % if object.error_details:
                <p><strong>Error Details:</strong></p>
                <pre>${object.error_details}</pre>
                % endif
                
                <p>Please check the integration logs for more details.</p>
                <p>Best regards,<br/>Odoo System</p>
            </div>
        </field>
        <field name="auto_delete">True</field>
    </record>
</odoo>
