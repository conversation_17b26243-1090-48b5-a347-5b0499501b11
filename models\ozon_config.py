# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import logging

_logger = logging.getLogger(__name__)


class OzonConfig(models.Model):
    _name = 'ozon.config'
    _description = 'Ozon API Configuration'
    _rec_name = 'name'

    name = fields.Char(
        string='Configuration Name',
        required=True,
        default='Ozon Integration'
    )
    
    # API Configuration
    client_id = fields.Char(
        string='Client ID',
        required=True,
        help='Ozon API Client ID from Seller Portal'
    )
    
    api_key = fields.Char(
        string='API Key',
        required=True,
        help='Ozon API Key from Seller Portal'
    )
    
    base_url = fields.Char(
        string='Base URL',
        default='https://api-seller.ozon.ru',
        required=True,
        help='Ozon API base URL'
    )
    
    # Integration Settings
    batch_size = fields.Integer(
        string='Batch Size',
        default=50,
        help='Number of items to process in each batch'
    )
    
    retry_attempts = fields.Integer(
        string='Retry Attempts',
        default=3,
        help='Number of retry attempts for failed API calls'
    )
    
    retry_delay = fields.Integer(
        string='Retry Delay (seconds)',
        default=5,
        help='Delay between retry attempts in seconds'
    )
    
    # Synchronization Settings
    auto_sync_products = fields.Boolean(
        string='Auto Sync Products',
        default=True,
        help='Automatically sync products to Ozon'
    )
    
    auto_sync_stock = fields.Boolean(
        string='Auto Sync Stock',
        default=True,
        help='Automatically sync stock quantities to Ozon'
    )
    
    auto_import_orders = fields.Boolean(
        string='Auto Import Orders',
        default=True,
        help='Automatically import orders from Ozon'
    )
    
    # Product Settings
    default_category_id = fields.Integer(
        string='Default Ozon Category ID',
        default=17028922,
        help='Default category ID for products in Ozon'
    )
    
    currency_code = fields.Char(
        string='Currency Code',
        default='RUB',
        help='Currency code for Ozon marketplace'
    )
    
    # Stock Settings
    stock_location_ids = fields.Many2many(
        'stock.location',
        string='Stock Locations',
        domain=[('usage', '=', 'internal')],
        help='Stock locations to consider for inventory sync'
    )
    
    # Order Settings
    order_days_back = fields.Integer(
        string='Order Import Days',
        default=7,
        help='Number of days back to import orders'
    )
    
    # Status and Logging
    active = fields.Boolean(
        string='Active',
        default=True
    )
    
    last_sync_date = fields.Datetime(
        string='Last Sync Date',
        readonly=True
    )
    
    last_sync_status = fields.Selection([
        ('success', 'Success'),
        ('error', 'Error'),
        ('warning', 'Warning')
    ], string='Last Sync Status', readonly=True)
    
    last_sync_message = fields.Text(
        string='Last Sync Message',
        readonly=True
    )
    
    # Statistics
    total_products_synced = fields.Integer(
        string='Products Synced',
        readonly=True,
        default=0
    )
    
    total_orders_imported = fields.Integer(
        string='Orders Imported',
        readonly=True,
        default=0
    )
    
    @api.constrains('client_id', 'api_key')
    def _check_api_credentials(self):
        """Validate API credentials format"""
        for record in self:
            if record.client_id and not record.client_id.isdigit():
                raise ValidationError(_('Client ID should contain only numbers'))
            if record.api_key and len(record.api_key) < 10:
                raise ValidationError(_('API Key seems too short'))
    
    @api.constrains('batch_size', 'retry_attempts', 'retry_delay')
    def _check_numeric_values(self):
        """Validate numeric configuration values"""
        for record in self:
            if record.batch_size <= 0:
                raise ValidationError(_('Batch size must be greater than 0'))
            if record.retry_attempts < 0:
                raise ValidationError(_('Retry attempts cannot be negative'))
            if record.retry_delay < 0:
                raise ValidationError(_('Retry delay cannot be negative'))
    
    def test_connection(self):
        """Test connection to Ozon API"""
        self.ensure_one()
        
        headers = {
            'Client-Id': self.client_id,
            'Api-Key': self.api_key,
            'Content-Type': 'application/json'
        }
        
        # Try different endpoints to find a working one
        test_endpoints = [
            '/v1/category/tree',
            '/v2/product/list',
            '/v3/product/info/stocks'
        ]
        
        for endpoint in test_endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                
                if endpoint == '/v1/category/tree':
                    payload = {'language': 'DEFAULT'}
                elif endpoint == '/v2/product/list':
                    payload = {'filter': {}, 'limit': 1, 'last_id': ''}
                else:
                    payload = {'filter': {'visibility': 'ALL'}, 'limit': 1}
                
                response = requests.post(url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    self.last_sync_status = 'success'
                    self.last_sync_message = f'Connection successful via {endpoint}'
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Success'),
                            'message': _('Connection to Ozon API successful!'),
                            'type': 'success',
                        }
                    }
                    
            except Exception as e:
                _logger.warning(f"Failed to test endpoint {endpoint}: {str(e)}")
                continue
        
        # If we get here, all endpoints failed
        self.last_sync_status = 'error'
        self.last_sync_message = 'All API endpoints failed. Check credentials and API access.'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Error'),
                'message': _('Failed to connect to Ozon API. Please check your credentials.'),
                'type': 'danger',
            }
        }
    
    def get_headers(self):
        """Get API headers for requests"""
        self.ensure_one()
        return {
            'Client-Id': self.client_id,
            'Api-Key': self.api_key,
            'Content-Type': 'application/json'
        }
    
    @api.model
    def get_active_config(self):
        """Get the active configuration"""
        config = self.search([('active', '=', True)], limit=1)
        if not config:
            raise ValidationError(_('No active Ozon configuration found. Please configure the integration first.'))
        return config
