#!/usr/bin/env python3
"""
Ozon Integration Addon Package Creator
=====================================

This script creates a clean addon package ready for deployment to Odoo server.
It excludes unnecessary files and creates a proper addon structure.
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_addon_package():
    """Create a clean addon package"""
    
    # Define source and target directories
    source_dir = Path(".")
    addon_name = "ozon_integration"
    package_dir = Path(f"../deploy/{addon_name}")
    
    # Files and directories to include in the addon
    include_patterns = [
        "__init__.py",
        "__manifest__.py", 
        "README.md",
        "models/",
        "views/",
        "wizard/",
        "controllers/",
        "data/",
        "security/",
        "demo/",
        "static/description/"
    ]
    
    # Files and directories to exclude
    exclude_patterns = [
        "venv/",
        "__pycache__/",
        "*.pyc",
        "*.pyo",
        ".git/",
        ".gitignore",
        "logs/",
        "INTEGRATION_STATUS.md",
        "create_addon_package.py",
        "deploy_instructions.md",
        ".env*",
        "requirements.txt"
    ]
    
    print(f"🚀 Creating Odoo addon package: {addon_name}")
    print("=" * 50)
    
    # Create package directory
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy files
    copied_files = []
    
    for pattern in include_patterns:
        source_path = source_dir / pattern
        
        if source_path.exists():
            if source_path.is_file():
                # Copy file
                target_path = package_dir / pattern
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source_path, target_path)
                copied_files.append(pattern)
                print(f"✅ Copied file: {pattern}")
                
            elif source_path.is_dir():
                # Copy directory
                target_path = package_dir / pattern
                shutil.copytree(source_path, target_path, 
                              ignore=shutil.ignore_patterns(*exclude_patterns))
                copied_files.append(pattern)
                print(f"✅ Copied directory: {pattern}")
        else:
            print(f"⚠️  Not found: {pattern}")
    
    # Create requirements.txt for the addon
    requirements_content = """# Ozon Integration Addon Requirements
# Install these packages on your Odoo server:

requests>=2.31.0
python-dotenv>=1.0.0
"""
    
    with open(package_dir / "requirements.txt", "w") as f:
        f.write(requirements_content)
    
    # Create installation script
    install_script = """#!/bin/bash
# Ozon Integration Addon Installation Script

echo "🚀 Installing Ozon Integration Addon"
echo "=================================="

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

# Set permissions
echo "🔒 Setting file permissions..."
chmod -R 755 .
find . -name "*.py" -exec chmod 644 {} \\;

echo "✅ Installation preparation complete!"
echo ""
echo "Next steps:"
echo "1. Copy this directory to your Odoo addons path"
echo "2. Add the path to odoo.conf addons_path"
echo "3. Restart Odoo service"
echo "4. Update Apps List in Odoo"
echo "5. Install 'Ozon Marketplace Integration' addon"
"""
    
    with open(package_dir / "install.sh", "w") as f:
        f.write(install_script)
    
    os.chmod(package_dir / "install.sh", 0o755)
    
    # Create deployment README
    deployment_readme = """# Ozon Integration Addon - Deployment Package

## Quick Installation

1. **Upload to server**: Copy this entire directory to your Odoo addons path
2. **Install dependencies**: Run `./install.sh` or `pip3 install -r requirements.txt`
3. **Set permissions**: Ensure odoo user owns the files
4. **Restart Odoo**: Restart your Odoo service
5. **Install addon**: Go to Apps → Update Apps List → Install "Ozon Marketplace Integration"

## Directory Structure

```
ozon_integration/
├── __init__.py              # Main addon init
├── __manifest__.py          # Addon manifest
├── models/                  # Data models
├── views/                   # XML views
├── wizard/                  # Sync wizards
├── controllers/             # Web controllers
├── data/                    # Default data
├── security/                # Access rights
├── demo/                    # Demo data
└── static/description/      # Addon description
```

## Configuration

After installation:
1. Go to **Ozon Integration → Configuration → Ozon Settings**
2. Enter your Ozon API credentials
3. Test connection
4. Enable sync on products
5. Start synchronizing!

## Support

Check the main README.md for detailed documentation and troubleshooting.
"""
    
    with open(package_dir / "DEPLOYMENT_README.md", "w") as f:
        f.write(deployment_readme)
    
    # Create ZIP package
    zip_path = package_dir.parent / f"{addon_name}.zip"
    
    print(f"\n📦 Creating ZIP package: {zip_path}")
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arc_path)
    
    # Summary
    print("\n" + "=" * 50)
    print("✅ Addon package created successfully!")
    print(f"📁 Package directory: {package_dir}")
    print(f"📦 ZIP package: {zip_path}")
    print(f"📄 Files included: {len(copied_files)}")
    
    print("\n🚀 Deployment Options:")
    print("1. Upload ZIP file and extract on server")
    print("2. Copy package directory directly to server")
    print("3. Use SCP/SFTP to transfer files")
    
    print("\n📋 Next Steps:")
    print("1. Transfer package to your Odoo server")
    print("2. Extract to Odoo addons directory")
    print("3. Run install.sh or install dependencies manually")
    print("4. Restart Odoo and install the addon")
    
    return package_dir, zip_path

if __name__ == "__main__":
    create_addon_package()
