# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class OzonSyncWizard(models.TransientModel):
    _name = 'ozon.sync.wizard'
    _description = 'Ozon Synchronization Wizard'

    sync_type = fields.Selection([
        ('products', 'Sync Products to Ozon'),
        ('stock', 'Sync Stock to Ozon'),
        ('orders', 'Import Orders from Ozon'),
        ('full', 'Full Synchronization')
    ], string='Sync Type', required=True, default='products')
    
    product_ids = fields.Many2many(
        'product.template',
        string='Products to Sync',
        domain=[('ozon_sync_enabled', '=', True)],
        help='Leave empty to sync all enabled products'
    )
    
    force_sync = fields.Boolean(
        string='Force Sync',
        default=False,
        help='Force sync even if products are already synced'
    )
    
    days_back = fields.Integer(
        string='Days Back',
        default=7,
        help='Number of days back to import orders (for order import)'
    )
    
    batch_size = fields.Integer(
        string='Batch Size',
        default=50,
        help='Number of items to process in each batch'
    )
    
    def action_start_sync(self):
        """Start the synchronization process"""
        self.ensure_one()
        
        # Validate configuration
        config = self.env['ozon.config'].search([('active', '=', True)], limit=1)
        if not config:
            raise UserError(_('No active Ozon configuration found. Please configure the integration first.'))
        
        # Create integration log
        log = self.env['ozon.integration.log'].create({
            'operation_type': self.sync_type if self.sync_type != 'full' else 'full_sync',
            'config_id': config.id,
            'status': 'running'
        })
        
        try:
            if self.sync_type == 'products':
                result = self._sync_products(config, log)
            elif self.sync_type == 'stock':
                result = self._sync_stock(config, log)
            elif self.sync_type == 'orders':
                result = self._import_orders(config, log)
            elif self.sync_type == 'full':
                result = self._full_sync(config, log)
            
            # Update log with results
            log.write({
                'status': 'success' if result['error_count'] == 0 else 'warning',
                'end_time': fields.Datetime.now(),
                'success_count': result['success_count'],
                'error_count': result['error_count'],
                'skipped_count': result.get('skipped_count', 0),
                'message': result['message']
            })
            
            return self._show_result_notification(result)
            
        except Exception as e:
            log.write({
                'status': 'error',
                'end_time': fields.Datetime.now(),
                'error_details': str(e)
            })
            raise UserError(_('Synchronization failed: %s') % str(e))
    
    def _sync_products(self, config, log):
        """Sync products to Ozon"""
        # Get products to sync
        if self.product_ids:
            products = self.product_ids
        else:
            domain = [('ozon_sync_enabled', '=', True), ('default_code', '!=', False)]
            if not self.force_sync:
                domain.append(('ozon_sync_status', 'in', ['not_synced', 'pending', 'error']))
            products = self.env['product.template'].search(domain, limit=self.batch_size)
        
        success_count = 0
        error_count = 0
        skipped_count = 0
        
        for product in products:
            try:
                if not product.default_code:
                    skipped_count += 1
                    continue
                    
                result = product.action_sync_to_ozon()
                if product.ozon_sync_status == 'synced':
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                _logger.error(f"Error syncing product {product.name}: {str(e)}")
        
        return {
            'success_count': success_count,
            'error_count': error_count,
            'skipped_count': skipped_count,
            'message': f'Product sync completed: {success_count} synced, {error_count} errors, {skipped_count} skipped'
        }
    
    def _sync_stock(self, config, log):
        """Sync stock to Ozon"""
        # Get products with stock to sync
        if self.product_ids:
            products = self.product_ids.filtered(lambda p: p.ozon_sync_status == 'synced')
        else:
            products = self.env['product.template'].search([
                ('ozon_sync_enabled', '=', True),
                ('ozon_sync_status', '=', 'synced'),
                ('default_code', '!=', False)
            ], limit=self.batch_size)
        
        success_count = 0
        error_count = 0
        
        for product in products:
            try:
                result = product.action_sync_stock_to_ozon()
                
                # Check if stock sync was successful
                stock_sync = self.env['ozon.stock.sync'].search([('product_id', '=', product.id)], limit=1)
                if stock_sync and stock_sync.sync_status == 'synced':
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                _logger.error(f"Error syncing stock for product {product.name}: {str(e)}")
        
        return {
            'success_count': success_count,
            'error_count': error_count,
            'message': f'Stock sync completed: {success_count} synced, {error_count} errors'
        }
    
    def _import_orders(self, config, log):
        """Import orders from Ozon"""
        try:
            # Use the sale order model's import method
            self.env['sale.order'].import_ozon_orders(self.days_back)
            
            # Get the latest log entry to get statistics
            latest_log = self.env['ozon.integration.log'].search([
                ('operation_type', '=', 'order_import'),
                ('id', '!=', log.id)
            ], limit=1, order='create_date desc')
            
            if latest_log:
                return {
                    'success_count': latest_log.success_count,
                    'error_count': latest_log.error_count,
                    'skipped_count': latest_log.skipped_count,
                    'message': latest_log.message or 'Order import completed'
                }
            else:
                return {
                    'success_count': 0,
                    'error_count': 0,
                    'skipped_count': 0,
                    'message': 'Order import completed (no new orders found)'
                }
                
        except Exception as e:
            _logger.error(f"Error importing orders: {str(e)}")
            return {
                'success_count': 0,
                'error_count': 1,
                'message': f'Order import failed: {str(e)}'
            }
    
    def _full_sync(self, config, log):
        """Perform full synchronization"""
        total_success = 0
        total_errors = 0
        total_skipped = 0
        messages = []
        
        # 1. Sync products
        try:
            product_result = self._sync_products(config, log)
            total_success += product_result['success_count']
            total_errors += product_result['error_count']
            total_skipped += product_result.get('skipped_count', 0)
            messages.append(f"Products: {product_result['success_count']} synced")
        except Exception as e:
            total_errors += 1
            messages.append(f"Product sync failed: {str(e)}")
        
        # 2. Sync stock
        try:
            stock_result = self._sync_stock(config, log)
            total_success += stock_result['success_count']
            total_errors += stock_result['error_count']
            messages.append(f"Stock: {stock_result['success_count']} synced")
        except Exception as e:
            total_errors += 1
            messages.append(f"Stock sync failed: {str(e)}")
        
        # 3. Import orders
        try:
            order_result = self._import_orders(config, log)
            total_success += order_result['success_count']
            total_errors += order_result['error_count']
            total_skipped += order_result.get('skipped_count', 0)
            messages.append(f"Orders: {order_result['success_count']} imported")
        except Exception as e:
            total_errors += 1
            messages.append(f"Order import failed: {str(e)}")
        
        return {
            'success_count': total_success,
            'error_count': total_errors,
            'skipped_count': total_skipped,
            'message': 'Full sync completed: ' + ', '.join(messages)
        }
    
    def _show_result_notification(self, result):
        """Show result notification to user"""
        if result['error_count'] == 0:
            notification_type = 'success'
            title = _('Sync Successful')
        else:
            notification_type = 'warning'
            title = _('Sync Completed with Errors')
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': title,
                'message': result['message'],
                'type': notification_type,
                'sticky': True,
            }
        }
