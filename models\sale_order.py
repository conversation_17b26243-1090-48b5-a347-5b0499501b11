# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import json
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Ozon Integration Fields
    ozon_order_id = fields.Char(
        string='Ozon Order ID',
        readonly=True,
        help='Ozon marketplace order identifier'
    )
    
    ozon_posting_number = fields.Char(
        string='Ozon Posting Number',
        readonly=True,
        help='Ozon posting number'
    )
    
    is_ozon_order = fields.Boolean(
        string='Ozon Order',
        default=False,
        readonly=True,
        help='Indicates if this order was imported from Ozon'
    )
    
    ozon_order_status = fields.Char(
        string='Ozon Order Status',
        readonly=True,
        help='Current status of the order in Ozon'
    )
    
    ozon_delivery_method = fields.Char(
        string='Ozon Delivery Method',
        readonly=True
    )
    
    ozon_payment_method = fields.Char(
        string='Ozon Payment Method',
        readonly=True
    )
    
    ozon_commission = fields.Float(
        string='Ozon Commission',
        readonly=True,
        help='Commission charged by Ozon'
    )
    
    ozon_import_date = fields.Datetime(
        string='Ozon Import Date',
        readonly=True,
        help='Date when order was imported from Ozon'
    )
    
    @api.model
    def import_ozon_orders(self, days_back=7):
        """Import orders from Ozon"""
        config = self.env['ozon.config'].get_active_config()
        
        if not config.auto_import_orders:
            _logger.info("Ozon order import is disabled in configuration")
            return
        
        # Create integration log
        log = self.env['ozon.integration.log'].create({
            'operation_type': 'order_import',
            'config_id': config.id,
            'status': 'running'
        })
        
        try:
            # Fetch orders from Ozon
            since_date = datetime.now() - timedelta(days=days_back)
            orders = self._fetch_ozon_orders(config, since_date)
            
            success_count = 0
            error_count = 0
            skipped_count = 0
            
            for order_data in orders:
                try:
                    result = self._process_ozon_order(order_data)
                    if result == 'created':
                        success_count += 1
                    elif result == 'skipped':
                        skipped_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    error_count += 1
                    _logger.error(f"Error processing Ozon order {order_data.get('posting_number')}: {str(e)}")
            
            # Update log
            log.write({
                'status': 'success' if error_count == 0 else 'warning',
                'end_time': fields.Datetime.now(),
                'success_count': success_count,
                'error_count': error_count,
                'skipped_count': skipped_count,
                'message': f'Imported {success_count} orders, {error_count} errors, {skipped_count} skipped'
            })
            
            # Update config statistics
            config.total_orders_imported += success_count
            config.last_sync_date = fields.Datetime.now()
            config.last_sync_status = 'success' if error_count == 0 else 'warning'
            
        except Exception as e:
            log.write({
                'status': 'error',
                'end_time': fields.Datetime.now(),
                'error_details': str(e)
            })
            _logger.error(f"Error importing Ozon orders: {str(e)}")
    
    def _fetch_ozon_orders(self, config, since_date):
        """Fetch orders from Ozon API"""
        headers = config.get_headers()
        url = f"{config.base_url}/v3/posting/fbs/list"
        
        payload = {
            'filter': {
                'since': since_date.isoformat(),
                'status': 'delivered'
            },
            'limit': 100,
            'offset': 0
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result.get('result', {}).get('postings', [])
            else:
                _logger.error(f"Failed to fetch Ozon orders: {response.status_code} - {response.text}")
                return []
        except Exception as e:
            _logger.error(f"Error fetching Ozon orders: {str(e)}")
            return []
    
    def _process_ozon_order(self, order_data):
        """Process a single Ozon order"""
        posting_number = order_data.get('posting_number')
        
        # Check if order already exists
        existing_order = self.search([('ozon_posting_number', '=', posting_number)], limit=1)
        if existing_order:
            return 'skipped'
        
        try:
            # Get or create customer
            partner = self._get_or_create_ozon_customer(order_data)
            
            # Prepare order data
            order_vals = {
                'partner_id': partner.id,
                'origin': f"OZON-{posting_number}",
                'client_order_ref': order_data.get('order_number'),
                'date_order': order_data.get('created_at'),
                'is_ozon_order': True,
                'ozon_order_id': order_data.get('order_id'),
                'ozon_posting_number': posting_number,
                'ozon_order_status': order_data.get('status'),
                'ozon_delivery_method': order_data.get('delivery_method', {}).get('name'),
                'ozon_import_date': fields.Datetime.now(),
                'order_line': self._prepare_ozon_order_lines(order_data.get('products', []))
            }
            
            # Create order
            order = self.create(order_vals)
            
            # Confirm order if configured
            if order.order_line:
                order.action_confirm()
            
            return 'created'
            
        except Exception as e:
            _logger.error(f"Error creating order from Ozon data: {str(e)}")
            return 'error'
    
    def _get_or_create_ozon_customer(self, order_data):
        """Get or create customer from Ozon order data"""
        addressee = order_data.get('addressee', {})
        customer_name = addressee.get('name', 'Ozon Customer')
        phone = addressee.get('phone', '')
        
        # Try to find existing customer by phone
        partner = None
        if phone:
            partner = self.env['res.partner'].search([('phone', '=', phone)], limit=1)
        
        if not partner:
            # Create new customer
            partner_vals = {
                'name': customer_name,
                'phone': phone,
                'is_company': False,
                'customer_rank': 1,
                'supplier_rank': 0,
                'comment': 'Customer imported from Ozon marketplace'
            }
            partner = self.env['res.partner'].create(partner_vals)
        
        return partner
    
    def _prepare_ozon_order_lines(self, products):
        """Prepare order lines from Ozon product data"""
        order_lines = []
        
        for product_data in products:
            # Find product by SKU
            offer_id = product_data.get('offer_id')
            sku = product_data.get('sku')
            
            product = None
            if offer_id:
                product = self.env['product.template'].search([('default_code', '=', offer_id)], limit=1)
            if not product and sku:
                product = self.env['product.template'].search([('barcode', '=', sku)], limit=1)
            
            if product:
                order_lines.append((0, 0, {
                    'product_id': product.product_variant_id.id,
                    'product_uom_qty': product_data.get('quantity', 1),
                    'price_unit': float(product_data.get('price', 0)),
                    'name': product_data.get('name', product.name)
                }))
            else:
                # Create a service product for unknown items
                service_product = self.env.ref('product.product_product_1', raise_if_not_found=False)
                if service_product:
                    order_lines.append((0, 0, {
                        'product_id': service_product.id,
                        'product_uom_qty': product_data.get('quantity', 1),
                        'price_unit': float(product_data.get('price', 0)),
                        'name': f"Ozon Product: {product_data.get('name', 'Unknown Product')}"
                    }))
        
        return order_lines
    
    @api.model
    def cron_import_ozon_orders(self):
        """Cron job to import orders from Ozon"""
        config = self.env['ozon.config'].search([('active', '=', True)], limit=1)
        if not config:
            return
        
        days_back = config.order_days_back or 7
        self.import_ozon_orders(days_back)
    
    def action_view_ozon_details(self):
        """View Ozon order details"""
        self.ensure_one()
        
        if not self.is_ozon_order:
            raise UserError(_('This is not an Ozon order'))
        
        return {
            'name': _('Ozon Order Details'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.id,
            'target': 'new',
        }
