<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Sale Order Form View Extension -->
    <record id="view_sale_order_form_ozon" model="ir.ui.view">
        <field name="name">sale.order.form.ozon</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <field name="client_order_ref" position="after">
                <field name="is_ozon_order" invisible="1"/>
                <field name="ozon_posting_number" attrs="{'invisible': [('is_ozon_order', '=', False)]}" readonly="1"/>
                <field name="ozon_order_status" attrs="{'invisible': [('is_ozon_order', '=', False)]}" readonly="1"/>
            </field>
            
            <notebook position="inside">
                <page string="Ozon Details" name="ozon_details" attrs="{'invisible': [('is_ozon_order', '=', False)]}">
                    <group>
                        <group string="Ozon Information">
                            <field name="ozon_order_id" readonly="1"/>
                            <field name="ozon_posting_number" readonly="1"/>
                            <field name="ozon_order_status" readonly="1"/>
                            <field name="ozon_import_date" readonly="1"/>
                        </group>
                        <group string="Delivery & Payment">
                            <field name="ozon_delivery_method" readonly="1"/>
                            <field name="ozon_payment_method" readonly="1"/>
                            <field name="ozon_commission" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <button name="action_view_ozon_details" string="View Full Ozon Details" 
                                type="object" class="btn-link" icon="fa-external-link"/>
                    </group>
                </page>
            </notebook>
        </field>
    </record>

    <!-- Sale Order Tree View Extension -->
    <record id="view_sale_order_tree_ozon" model="ir.ui.view">
        <field name="name">sale.order.tree.ozon</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_quotation_tree"/>
        <field name="arch" type="xml">
            <field name="amount_total" position="after">
                <field name="is_ozon_order" optional="hide"/>
                <field name="ozon_posting_number" optional="hide"/>
                <field name="ozon_order_status" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- Sale Order Search View Extension -->
    <record id="view_sale_order_search_ozon" model="ir.ui.view">
        <field name="name">sale.order.search.ozon</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_sales_order_filter"/>
        <field name="arch" type="xml">
            <filter name="my_sale_orders_filter" position="after">
                <separator/>
                <filter string="Ozon Orders" name="ozon_orders" 
                        domain="[('is_ozon_order', '=', True)]"/>
                <filter string="Regular Orders" name="regular_orders" 
                        domain="[('is_ozon_order', '=', False)]"/>
            </filter>
            <field name="partner_id" position="after">
                <field name="ozon_posting_number"/>
                <field name="ozon_order_id"/>
            </field>
            <group expand="0" string="Group By" position="inside">
                <filter string="Order Source" name="group_order_source" 
                        context="{'group_by': 'is_ozon_order'}"/>
                <filter string="Ozon Status" name="group_ozon_status" 
                        context="{'group_by': 'ozon_order_status'}"/>
            </group>
        </field>
    </record>

    <!-- Ozon Orders Action -->
    <record id="action_ozon_orders" model="ir.actions.act_window">
        <field name="name">Ozon Orders</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('is_ozon_order', '=', True)]</field>
        <field name="context">{'search_default_ozon_orders': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No orders imported from Ozon yet
            </p>
            <p>
                Orders will appear here automatically when imported from Ozon marketplace.
            </p>
        </field>
    </record>
</odoo>
