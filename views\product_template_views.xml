<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Template Form View Extension -->
    <record id="view_product_template_form_ozon" model="ir.ui.view">
        <field name="name">product.template.form.ozon</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <notebook position="inside">
                <page string="Ozon Integration" name="ozon_integration">
                    <group>
                        <group string="Sync Settings">
                            <field name="ozon_sync_enabled"/>
                            <field name="ozon_offer_id" attrs="{'required': [('ozon_sync_enabled', '=', True)]}"/>
                            <field name="ozon_category_id"/>
                        </group>
                        <group string="Status">
                            <field name="ozon_sync_status" readonly="1"/>
                            <field name="ozon_last_sync_date" readonly="1"/>
                            <field name="ozon_stock_qty" readonly="1"/>
                        </group>
                    </group>
                    
                    <group string="Ozon Pricing" attrs="{'invisible': [('ozon_sync_enabled', '=', False)]}">
                        <group>
                            <field name="ozon_price"/>
                            <field name="ozon_old_price"/>
                        </group>
                    </group>
                    
                    <group string="Sync Actions" attrs="{'invisible': [('ozon_sync_enabled', '=', False)]}">
                        <button name="action_sync_to_ozon" string="Sync Product to Ozon" 
                                type="object" class="btn-primary" icon="fa-cloud-upload"/>
                        <button name="action_sync_stock_to_ozon" string="Sync Stock to Ozon" 
                                type="object" class="btn-secondary" icon="fa-cubes"/>
                        <button name="action_view_ozon_sync_logs" string="View Sync Logs" 
                                type="object" class="btn-link" icon="fa-list"/>
                    </group>
                    
                    <group string="Last Error" attrs="{'invisible': [('ozon_last_error', '=', False)]}">
                        <field name="ozon_last_error" nolabel="1" readonly="1"/>
                    </group>
                </page>
            </notebook>
        </field>
    </record>

    <!-- Product Template Tree View Extension -->
    <record id="view_product_template_tree_ozon" model="ir.ui.view">
        <field name="name">product.template.tree.ozon</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <field name="list_price" position="after">
                <field name="ozon_sync_enabled" optional="hide"/>
                <field name="ozon_sync_status" optional="hide"/>
                <field name="ozon_stock_qty" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- Product Template Search View Extension -->
    <record id="view_product_template_search_ozon" model="ir.ui.view">
        <field name="name">product.template.search.ozon</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <filter name="filter_to_sell" position="after">
                <separator/>
                <filter string="Ozon Sync Enabled" name="ozon_sync_enabled" 
                        domain="[('ozon_sync_enabled', '=', True)]"/>
                <filter string="Synced to Ozon" name="ozon_synced" 
                        domain="[('ozon_sync_status', '=', 'synced')]"/>
                <filter string="Sync Pending" name="ozon_pending" 
                        domain="[('ozon_sync_status', '=', 'pending')]"/>
                <filter string="Sync Error" name="ozon_error" 
                        domain="[('ozon_sync_status', '=', 'error')]"/>
            </filter>
            <group expand="0" string="Group By" position="inside">
                <filter string="Ozon Sync Status" name="group_ozon_sync_status" 
                        context="{'group_by': 'ozon_sync_status'}"/>
            </group>
        </field>
    </record>

    <!-- Product Variant Form View Extension -->
    <record id="view_product_product_form_ozon" model="ir.ui.view">
        <field name="name">product.product.form.ozon</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <group name="group_general" position="after">
                <group string="Ozon Integration" name="ozon_integration" 
                       attrs="{'invisible': [('product_tmpl_id.ozon_sync_enabled', '=', False)]}">
                    <field name="product_tmpl_id.ozon_sync_status" readonly="1"/>
                    <field name="product_tmpl_id.ozon_last_sync_date" readonly="1"/>
                    <button name="action_sync_to_ozon" string="Sync to Ozon" 
                            type="object" class="btn-primary" icon="fa-cloud-upload"/>
                </group>
            </group>
        </field>
    </record>

    <!-- Ozon Products Action -->
    <record id="action_ozon_products" model="ir.actions.act_window">
        <field name="name">Ozon Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('ozon_sync_enabled', '=', True)]</field>
        <field name="context">{'search_default_ozon_sync_enabled': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No products configured for Ozon sync
            </p>
            <p>
                Enable Ozon sync on your products to start synchronizing them with the marketplace.
            </p>
        </field>
    </record>
</odoo>
