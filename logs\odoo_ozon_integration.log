2025-09-03 22:43:47,925 - OdooOzonIntegration - INFO - [CONFIG_VALIDATION] Validating configuration...
2025-09-03 22:43:47,926 - OdooOzonIntegration - INFO - [CONFIG_VALIDATION] Configuration validation successful
2025-09-03 22:46:08,889 - OdooOzonIntegration - INFO - [INTEGRATION_START] Starting full Odoo-Ozon integration
2025-09-03 22:46:08,890 - OdooOzonIntegration - INFO - [CONFIG_VALIDATION] Validating configuration...
2025-09-03 22:46:08,890 - OdooOzonIntegration - INFO - [CONFIG_VALIDATION] Configuration validation successful
2025-09-03 22:46:08,891 - OdooOzonIntegration - INFO - [INTEGRATION_PHASE] Phase 1: Product Synchronization
2025-09-03 22:46:08,895 - OdooOzonIntegration - INFO - [PRODUCT_SYNC_START] Starting product synchronization from Odoo to Ozon
2025-09-03 22:46:08,896 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Attempting to connect to Odoo...
2025-09-03 22:46:10,258 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Successfully connected to Odoo
2025-09-03 22:46:10,259 - OdooOzonIntegration - INFO - [ODOO_PRODUCTS] Fetching products from Odoo...
2025-09-03 22:46:11,644 - OdooOzonIntegration - INFO - [ODOO_PRODUCTS] Fetched 4 products from Odoo
2025-09-03 22:46:11,644 - OdooOzonIntegration - INFO - [PRODUCT_SYNC_PROGRESS] Processing 4 products
2025-09-03 22:46:11,645 - OdooOzonIntegration - INFO - [PRODUCT_SYNC_BATCH] Processing batch 1
2025-09-03 22:46:11,645 - OdooOzonIntegration - INFO - [OZON_PRODUCT_SYNC] Syncing product: Discount
2025-09-03 22:46:12,545 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: Attempt 1/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:18,095 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: Attempt 2/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:23,658 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: Attempt 3/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:23,661 - OdooOzonIntegration - ERROR - [OZON_PRODUCT_SEARCH] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: SKU: DISC
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 367, in _find_product_by_sku
    response = self._make_request("POST", "/v2/product/list", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:24,170 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Attempt 1/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:29,721 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Attempt 2/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:35,253 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Attempt 3/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:35,258 - OdooOzonIntegration - ERROR - [OZON_PRODUCT_CREATE] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Product: Discount
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 386, in _create_product
    response = self._make_request("POST", "/v3/product/import", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:35,263 - OdooOzonIntegration - INFO - [PRODUCT_SYNC] Product SYNC: ID=N/A, Name='Down Payment (POS)', Status=SKIPPED, Details=No SKU/default_code
2025-09-03 22:46:35,264 - OdooOzonIntegration - INFO - [PRODUCT_SYNC] Product SYNC: ID=N/A, Name='Laptop Dell Latitude 7420 Ci7 11Th Gen 16GB 512SSD', Status=SKIPPED, Details=No SKU/default_code
2025-09-03 22:46:35,265 - OdooOzonIntegration - INFO - [OZON_PRODUCT_SYNC] Syncing product: Tips
2025-09-03 22:46:35,777 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: Attempt 1/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:41,306 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: Attempt 2/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:46,847 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: Attempt 3/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:46,851 - OdooOzonIntegration - ERROR - [OZON_PRODUCT_SEARCH] ERROR: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list | Context: SKU: TIPS
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 367, in _find_product_by_sku
    response = self._make_request("POST", "/v2/product/list", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://api-seller.ozon.ru/v2/product/list

2025-09-03 22:46:47,391 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Attempt 1/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:52,906 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Attempt 2/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:58,441 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Attempt 3/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:58,444 - OdooOzonIntegration - ERROR - [OZON_PRODUCT_CREATE] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import | Context: Product: Tips
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 386, in _create_product
    response = self._make_request("POST", "/v3/product/import", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/product/import

2025-09-03 22:46:59,447 - OdooOzonIntegration - INFO - [PRODUCT_SYNC_COMPLETE] Product sync completed. Success: 0, Failed: 2, Skipped: 2
2025-09-03 22:46:59,447 - OdooOzonIntegration - INFO - [INTEGRATION_PHASE] Phase 2: Stock Synchronization
2025-09-03 22:46:59,448 - OdooOzonIntegration - INFO - [STOCK_SYNC_START] Starting stock synchronization from Odoo to Ozon
2025-09-03 22:46:59,449 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Attempting to connect to Odoo...
2025-09-03 22:47:00,912 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Successfully connected to Odoo
2025-09-03 22:47:00,913 - OdooOzonIntegration - INFO - [ODOO_PRODUCTS] Fetching products from Odoo...
2025-09-03 22:47:02,164 - OdooOzonIntegration - INFO - [ODOO_PRODUCTS] Fetched 4 products from Odoo
2025-09-03 22:47:02,165 - OdooOzonIntegration - INFO - [ODOO_STOCK] Fetching stock for 2 products...
2025-09-03 22:47:03,724 - OdooOzonIntegration - INFO - [ODOO_STOCK] Retrieved stock data for 2 products
2025-09-03 22:47:03,725 - OdooOzonIntegration - INFO - [OZON_STOCK_UPDATE] Updating stock for 2 products
2025-09-03 22:47:04,260 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks | Context: Attempt 1/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks

2025-09-03 22:47:09,783 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks | Context: Attempt 2/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks

2025-09-03 22:47:15,297 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks | Context: Attempt 3/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks

2025-09-03 22:47:15,299 - OdooOzonIntegration - ERROR - [OZON_STOCK_UPDATE] ERROR: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 489, in update_stock
    response = self._make_request("POST", "/v1/product/import/stocks", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 410 Client Error: Gone for url: https://api-seller.ozon.ru/v1/product/import/stocks

2025-09-03 22:47:15,302 - OdooOzonIntegration - ERROR - [STOCK_SYNC_COMPLETE] Failed to update stock for 2 products
2025-09-03 22:47:15,307 - OdooOzonIntegration - INFO - [INTEGRATION_PHASE] Phase 3: Order Synchronization
2025-09-03 22:47:15,309 - OdooOzonIntegration - INFO - [ORDER_SYNC_START] Starting order synchronization from Ozon to Odoo (last 7 days)
2025-09-03 22:47:15,309 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Attempting to connect to Odoo...
2025-09-03 22:47:16,686 - OdooOzonIntegration - INFO - [ODOO_CONNECTION] Successfully connected to Odoo
2025-09-03 22:47:16,687 - OdooOzonIntegration - INFO - [OZON_ORDERS] Fetching orders since 2025-08-27 22:47:16.687402
2025-09-03 22:47:17,217 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list | Context: Attempt 1/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list

2025-09-03 22:47:22,738 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list | Context: Attempt 2/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list

2025-09-03 22:47:28,565 - OdooOzonIntegration - ERROR - [OZON_API] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list | Context: Attempt 3/3
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list

2025-09-03 22:47:28,569 - OdooOzonIntegration - ERROR - [OZON_ORDERS] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 524, in get_orders
    response = self._make_request("POST", "/v3/posting/fbs/list", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list

2025-09-03 22:47:28,575 - OdooOzonIntegration - ERROR - [ORDER_SYNC] ERROR: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 683, in sync_orders_from_ozon
    ozon_orders = self.ozon.get_orders(since_date)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 524, in get_orders
    response = self._make_request("POST", "/v3/posting/fbs/list", payload)
  File "C:\Users\<USER>\Desktop\Odoo\addons\products_from_odoo\venv\integrate_odoo_ozon.py", line 299, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\python\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error: Bad Request for url: https://api-seller.ozon.ru/v3/posting/fbs/list

2025-09-03 22:47:28,580 - OdooOzonIntegration - INFO - [ORDER_SYNC_COMPLETE] Order sync completed. Success: 0, Failed: 0, Skipped: 0
2025-09-03 22:47:28,581 - OdooOzonIntegration - INFO - [INTEGRATION_COMPLETE] Full integration completed in 79.69 seconds
2025-09-03 22:47:28,582 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] === INTEGRATION SUMMARY ===
2025-09-03 22:47:28,583 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] PRODUCTS:
2025-09-03 22:47:28,584 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   success: 0
2025-09-03 22:47:28,584 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   failed: 2
2025-09-03 22:47:28,585 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   skipped: 2
2025-09-03 22:47:28,585 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] STOCK:
2025-09-03 22:47:28,586 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   success: 0
2025-09-03 22:47:28,587 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   failed: 2
2025-09-03 22:47:28,587 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] ORDERS:
2025-09-03 22:47:28,588 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   success: 0
2025-09-03 22:47:28,589 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   failed: 0
2025-09-03 22:47:28,589 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY]   skipped: 0
2025-09-03 22:47:28,590 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] TOTAL SUCCESS: 0
2025-09-03 22:47:28,591 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] TOTAL FAILED: 4
2025-09-03 22:47:28,591 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] TOTAL SKIPPED: 2
2025-09-03 22:47:28,592 - OdooOzonIntegration - INFO - [INTEGRATION_SUMMARY] === END SUMMARY ===
