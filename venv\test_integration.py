#!/usr/bin/env python3
"""
Test script for Odoo-Ozon Integration System
============================================

This script provides basic tests and validation for the integration system.
Run this before executing the full integration to ensure everything is configured correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment_variables():
    """Test if all required environment variables are set"""
    print("🔍 Testing Environment Variables...")
    
    required_vars = ['OZON_CLIENT_ID', 'OZON_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("   Please check your .env file")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("\n🔍 Testing Dependencies...")
    
    required_modules = [
        ('requests', 'requests'),
        ('odoorpc', 'OdooRPC'),
        ('dotenv', 'python-dotenv')
    ]
    
    missing_modules = []
    
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {package_name} is installed")
        except ImportError:
            missing_modules.append(package_name)
            print(f"❌ {package_name} is not installed")
    
    if missing_modules:
        print(f"\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_modules)}")
        return False
    
    return True

def test_odoo_connection():
    """Test connection to Odoo"""
    print("\n🔍 Testing Odoo Connection...")
    
    try:
        import odoorpc

        # Use the same connection parameters as in main.py
        connection = odoorpc.ODOO(host="**************", port=8069)
        connection.login(
            "my-odoo-db",
            "<EMAIL>",
            "GoldLeaf@@786"
        )

        # Test by fetching a small number of products
        product_ids = connection.env['product.template'].search([], limit=1)
        products = connection.env['product.template'].read(product_ids, ['name'])

        print(f"✅ Odoo connection successful - found {len(products)} test product(s)")
        return True
        
    except Exception as e:
        print(f"❌ Odoo connection failed: {str(e)}")
        return False

def test_ozon_connection():
    """Test connection to Ozon API"""
    print("\n🔍 Testing Ozon API Connection...")
    
    try:
        import requests
        
        client_id = os.getenv("OZON_CLIENT_ID")
        api_key = os.getenv("OZON_API_KEY")
        
        headers = {
            "Client-Id": client_id,
            "Api-Key": api_key,
            "Content-Type": "application/json"
        }
        
        # Test with a simple API call - try different endpoint
        url = "https://api-seller.ozon.ru/v1/product/list"
        payload = {
            "filter": {},
            "limit": 1,
            "last_id": ""
        }

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            if 'result' in result:
                print("✅ Ozon API connection successful")
                return True
            else:
                print("❌ Ozon API returned unexpected response")
                return False
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                # Try alternative endpoint
                url = "https://api-seller.ozon.ru/v3/product/info/stocks"
                payload = {"filter": {"visibility": "ALL"}, "limit": 1}
                try:
                    response = requests.post(url, headers=headers, json=payload, timeout=30)
                    response.raise_for_status()
                    print("✅ Ozon API connection successful (alternative endpoint)")
                    return True
                except:
                    print("❌ Ozon API connection failed - check credentials and API access")
                    return False
            else:
                raise
            
    except Exception as e:
        print(f"❌ Ozon API connection failed: {str(e)}")
        return False

def test_integration_system():
    """Test the integration system components"""
    print("\n🔍 Testing Integration System...")
    
    try:
        # Test if the integration file exists and can be imported
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        integration_file = os.path.join(current_dir, 'integrate_odoo_ozon.py')

        if not os.path.exists(integration_file):
            print("❌ Integration system file not found")
            return False

        # Try to import the integration system
        sys.path.insert(0, current_dir)

        try:
            import integrate_odoo_ozon
            OdooOzonIntegrator = integrate_odoo_ozon.OdooOzonIntegrator
            IntegrationConfig = integrate_odoo_ozon.IntegrationConfig

            # Initialize with test configuration
            config = IntegrationConfig()
            integrator = OdooOzonIntegrator(config)

            # Test configuration validation
            if integrator.validate_configuration():
                print("✅ Integration system configuration is valid")
                return True
            else:
                print("❌ Integration system configuration is invalid")
                return False

        except ImportError as ie:
            print(f"❌ Failed to import integration system: {str(ie)}")
            return False

    except Exception as e:
        print(f"❌ Integration system test failed: {str(e)}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("🚀 Starting Integration System Tests")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Dependencies", test_dependencies),
        ("Odoo Connection", test_odoo_connection),
        ("Ozon API Connection", test_ozon_connection),
        ("Integration System", test_integration_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("\n🎉 All tests passed! The integration system is ready to use.")
        print("   Run 'python integrate-odoo-ozon.py' to start the integration.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please fix the issues before running the integration.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
