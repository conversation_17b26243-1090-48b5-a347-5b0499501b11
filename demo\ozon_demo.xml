<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo Ozon Configuration -->
    <record id="demo_ozon_config" model="ozon.config">
        <field name="name">Demo Ozon Configuration</field>
        <field name="client_id">demo_client_id</field>
        <field name="api_key">demo_api_key</field>
        <field name="base_url">https://api-seller.ozon.ru</field>
        <field name="batch_size">10</field>
        <field name="retry_attempts">2</field>
        <field name="retry_delay">3</field>
        <field name="auto_sync_products">False</field>
        <field name="auto_sync_stock">False</field>
        <field name="auto_import_orders">False</field>
        <field name="default_category_id">17028922</field>
        <field name="currency_code">RUB</field>
        <field name="order_days_back">3</field>
        <field name="active">False</field>
    </record>

    <!-- Demo Product with Ozon Integration -->
    <record id="demo_product_ozon" model="product.template">
        <field name="name">Demo Ozon Product</field>
        <field name="default_code">DEMO-OZON-001</field>
        <field name="list_price">100.0</field>
        <field name="standard_price">50.0</field>
        <field name="type">product</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="sale_ok">True</field>
        <field name="purchase_ok">True</field>
        <field name="ozon_sync_enabled">True</field>
        <field name="ozon_offer_id">DEMO-OZON-001</field>
        <field name="ozon_category_id">17028922</field>
        <field name="ozon_price">100.0</field>
        <field name="ozon_old_price">120.0</field>
        <field name="ozon_sync_status">not_synced</field>
    </record>

    <!-- Demo Product Sync Record -->
    <record id="demo_product_sync" model="ozon.product.sync">
        <field name="product_id" ref="demo_product_ozon"/>
        <field name="ozon_offer_id">DEMO-OZON-001</field>
        <field name="sync_status">pending</field>
        <field name="ozon_category_id">17028922</field>
        <field name="ozon_price">100.0</field>
        <field name="ozon_stock">0</field>
    </record>

    <!-- Demo Stock Sync Record -->
    <record id="demo_stock_sync" model="ozon.stock.sync">
        <field name="product_id" ref="demo_product_ozon"/>
        <field name="odoo_stock">50.0</field>
        <field name="ozon_stock">0</field>
        <field name="sync_status">pending</field>
    </record>

    <!-- Demo Integration Log -->
    <record id="demo_integration_log" model="ozon.integration.log">
        <field name="operation_type">product_sync</field>
        <field name="status">success</field>
        <field name="start_time" eval="(DateTime.now() - timedelta(hours=1))"/>
        <field name="end_time" eval="DateTime.now()"/>
        <field name="success_count">5</field>
        <field name="error_count">0</field>
        <field name="skipped_count">2</field>
        <field name="message">Demo sync completed successfully</field>
        <field name="config_id" ref="demo_ozon_config"/>
    </record>

    <!-- Demo Customer for Ozon Orders -->
    <record id="demo_ozon_customer" model="res.partner">
        <field name="name">Ozon Demo Customer</field>
        <field name="phone">******-123-4567</field>
        <field name="email"><EMAIL></field>
        <field name="is_company">False</field>
        <field name="customer_rank">1</field>
        <field name="comment">Demo customer imported from Ozon marketplace</field>
    </record>

    <!-- Demo Ozon Order -->
    <record id="demo_ozon_order" model="sale.order">
        <field name="partner_id" ref="demo_ozon_customer"/>
        <field name="origin">OZON-DEMO-12345</field>
        <field name="client_order_ref">DEMO-ORDER-001</field>
        <field name="is_ozon_order">True</field>
        <field name="ozon_order_id">demo_order_123</field>
        <field name="ozon_posting_number">DEMO-12345</field>
        <field name="ozon_order_status">delivered</field>
        <field name="ozon_delivery_method">Ozon Delivery</field>
        <field name="ozon_payment_method">Card</field>
        <field name="ozon_commission">15.0</field>
        <field name="ozon_import_date" eval="DateTime.now()"/>
        <field name="state">draft</field>
    </record>

    <!-- Demo Order Line -->
    <record id="demo_ozon_order_line" model="sale.order.line">
        <field name="order_id" ref="demo_ozon_order"/>
        <field name="product_id" ref="demo_product_ozon"/>
        <field name="product_uom_qty">2</field>
        <field name="price_unit">100.0</field>
        <field name="name">Demo Ozon Product</field>
    </record>
</odoo>
